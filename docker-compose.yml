services:
  # MySQL 主数据库
  mysql:
    image: mysql:8.0
    container_name: kip-billing-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: db_prod_kip_billing
      MYSQL_USER: prod_kip_billing
      MYSQL_PASSWORD: prod_kip_billing123
    ports:
      - "3320:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    networks:
      - kip-billing-network
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # Oracle JDE数据库
  oracle:
    image: gvenzl/oracle-xe:21-slim
    container_name: kip-billing-oracle
    environment:
      ORACLE_PASSWORD: Oracle123
      ORACLE_DATABASE: JDEPRD
    ports:
      - "1521:1521"
    volumes:
      - oracle_data:/opt/oracle/oradata
      - ./docker/oracle/init:/container-entrypoint-initdb.d
    networks:
      - kip-billing-network

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: kip-billing-redis
    ports:
      - "6390:6379"
    volumes:
      - redis_data:/data
    networks:
      - kip-billing-network
    command: redis-server --appendonly yes


volumes:
  mysql_data:
  oracle_data:
  redis_data:

networks:
  kip-billing-network:
    driver: bridge
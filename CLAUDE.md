# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build & Development Commands

### Maven Commands
- `mvn clean compile` - 清理并编译项目
- `mvn test` - 运行所有测试用例
- `mvn test -Dtest=ClassName#methodName` - 运行单个测试方法
- `mvn spring-boot:run` - 启动应用程序
- `mvn package` - 打包应用程序
- `mvn jacoco:report` - 生成测试覆盖率报告

### Testing
- 测试配置文件: `src/test/resources/application-test.yml`
- 集成测试基类: `BaseIntegrationTest` - 提供通用测试工具和mock配置
- 单元测试使用H2内存数据库，支持MySQL和Oracle模式
- 使用WireMock进行外部服务mock
- 测试方法命名规范：以 `should` 开头，遵循AAA模式（Arrange, Act, Assert）

## Project Architecture

### Core Technology Stack
- **Spring Boot 3.2.2** 配合 **JDK 17**
- **Spring Data JPA** + **QueryDSL** 用于数据访问
- **MySQL** (主数据库) + **Oracle** (JDE系统集成)
- **Redis** 用于缓存和分布式锁
- **OpenFeign** 用于微服务通信
- **Log4j2** 日志框架

### Architecture Layers

#### 1. Web Layer (`webservice/`)
- **Facade接口**: 定义业务门面接口
- **Controller实现**: 在 `webservice/impl/` 中实现具体的REST接口
- **VO对象**: `webservice/vo/req/` (请求) 和 `webservice/vo/resp/` (响应)
- **定时任务**: `webservice/scheduler/` 包含各种定时同步任务

#### 2. Service Layer (`service/`)
- **接口定义**: 业务逻辑接口
- **实现类**: `service/impl/` 中包含具体业务实现
- **核心服务**:
  - `IBillService` - 账单核心服务
  - `AptPayService` - 公寓支付服务
  - `EFapiaoBillService` - 电子发票服务
  - `CounterCashierService` - 柜台收银服务

#### 3. Data Access Layer (`dao/`)
- **Repository接口**: 继承自 `BaseJpaRepository`，支持QueryDSL
- **Entity实体**: `dao/entity/` 中的JPA实体类
- **类型转换器**: `dao/convert/` 中的JPA属性转换器
- **JDBC实现**: `dao/impl/` 中的复杂查询JDBC实现

#### 4. External Integration (`feign/`)
- **Client接口**: `feign/clients/` 定义外部服务调用
- **DTO对象**: `feign/entity/` 包含外部服务数据传输对象
- **配置**: `feign/config/` 包含Feign客户端配置

#### 5. Common Components (`common/`)
- **枚举**: `common/enums/` - 业务枚举类型
- **工具类**: `common/utils/` - 通用工具方法
- **异常**: `common/exceptions/` - 自定义异常类
- **AOP**: `common/aop/` - 切面编程（签名验证、分布式锁等）
- **JPA扩展**: `common/jpa/` - 自定义JPA Repository基类

### Key Business Domains

1. **Bill Management (账单管理)**
   - 公寓账单生成和管理
   - JDE系统同步
   - 账单发送和通知

2. **Payment Processing (支付处理)**
   - 多种支付方式支持（微信、支付宝、银行卡）
   - 代扣协议管理
   - 支付回调处理

3. **Invoice System (发票系统)**
   - 电子发票生成
   - 发票上传到税务系统
   - 发票状态同步

4. **Counter Cashier (柜台收银)**
   - 线下收银功能
   - 杂费收取
   - 收银员操作

### Configuration Profiles
- `application-local.yml` - 本地开发环境
- `application-dev.yml` - 开发环境
- `application-qa.yml`, `application-qa-apollo.yml` - 测试环境
- `application-prod.yml`, `application-proda.yml`, `application-prodb.yml` - 生产环境
- `application-test.yml` - 单元测试环境

### Database Integration
- **主数据库**: MySQL，用于业务数据存储
- **JDE系统**: Oracle数据库，用于与企业ERP系统集成
- **多数据源配置**: `MultiDatasourceConfig` 管理多个数据源
- **数据库脚本**: `src/main/resources/script/` 包含版本迁移脚本

### Message and Notification
- 支持邮件、短信、微信小程序消息推送
- 使用统一消息服务进行外部通知
- 消息模板管理和个性化配置

### Security & Validation
- 业务签名验证 (`SignValidAop`)
- 用户认证和权限控制
- 接口参数校验和数据脱敏

### Logging & Monitoring
- Log4j2配置支持日志脱敏
- 业务操作日志记录
- 集成链路追踪 (Spring Cloud Sleuth)

## Development Guidelines

### Code Standards
- 使用Lombok减少样板代码
- 所有注释必须使用中文
- 遵循CLEAN, SOLID, DRY原则
- 优先使用Lambda表达式和Stream API
- 使用Optional避免NPE

### Database Conventions
- Entity类放在 `dao/entity/` 包下
- Repository接口继承 `BaseJpaRepository`
- 复杂查询使用QueryDSL或JDBC实现
- 数据库字段命名遵循下划线命名法

### Testing Conventions
- 集成测试继承 `BaseIntegrationTest`
- 测试方法以 `should` 开头
- 使用 `@SpyBean` 而非 `@MockBean` 避免容器重启
- 外部服务调用使用WireMock进行mock

### Git Conventions  
- 提交信息格式: `KIP-XXXX 中文描述`
- JIRA ID可从分支名称中提取
- 主分支: `develop`
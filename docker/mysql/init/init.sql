SET NAMES utf8;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS db_prod_kip_billing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE db_prod_kip_billing;

-- ----------------------------
--  Table structure for `apt_bill`
--  query by: project_id is null
--  query by: bill_no, payment_status, push_status, room_id, building_id
-- ----------------------------
DROP TABLE IF EXISTS `apt_bill`;
CREATE TABLE `apt_bill` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `bill_no` varchar(32) NOT NULL DEFAULT '',
  `category` varchar(64) DEFAULT NULL,
  `bu` varchar(32) NOT NULL DEFAULT '',
  `unit` varchar(32) NOT NULL DEFAULT '',
  `an8` varchar(32) DEFAULT NULL,
  `alph` varchar(128) DEFAULT NULL,
  `doco` varchar(64) DEFAULT NULL,
  `begin_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `year` int DEFAULT NULL,
  `month` int DEFAULT NULL,
  `bill_month` int DEFAULT NULL,
  `amt` decimal(32,2) NOT NULL,
  `status` varchar(32) NOT NULL DEFAULT '',
  `payment_status` varchar(32) DEFAULT NULL,
  `payment_result` varchar(256) DEFAULT NULL,
  `push_status` varchar(32) DEFAULT NULL,
  `project_id` varchar(32) DEFAULT NULL,
  `building_id` varchar(32) DEFAULT NULL,
  `floor_id` varchar(32) DEFAULT NULL,
  `room_id` varchar(32) DEFAULT NULL,
  `position_item` varchar(500) DEFAULT NULL,
  `deleted_at` int NOT NULL DEFAULT '0',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `pay_session` varchar(128) DEFAULT NULL COMMENT '支付session',
  `rd_glc` varchar(32) DEFAULT NULL COMMENT '票据码',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  PRIMARY KEY (`id`),
  KEY `idx_bill_no` (`bill_no`) comment 'index for biz bill number',
  KEY `idx_building_id` (`building_id`) comment 'index for building id',
  KEY `idx_room_id` (`room_id`) comment 'index for room ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- 插入一些测试数据
INSERT INTO `apt_bill` (`bill_no`, `category`, `bu`, `unit`, `an8`, `alph`, `doco`, `begin_date`, `end_date`, `year`, `month`, `bill_month`, `amt`, `status`, `payment_status`, `project_id`, `building_id`, `room_id`) VALUES
('BILL001', '物业费', '31074', 'UNIT001', '12345', '张三', 'CONTRACT001', '2024-01-01 00:00:00', '2024-01-31 23:59:59', 2024, 1, 1, 1500.00, 'NORMAL', 'UNPAID', 'P001', 'B001', 'R001'),
('BILL002', '停车费', '31074', 'UNIT002', '12346', '李四', 'CONTRACT002', '2024-01-01 00:00:00', '2024-01-31 23:59:59', 2024, 1, 1, 800.00, 'NORMAL', 'PAID', 'P001', 'B001', 'R002');

-- 其他必要的表结构（简化版，仅包含核心表）
CREATE TABLE IF NOT EXISTS `apt_payment_info` (
  `id` varchar(32) NOT NULL,
  `amt` decimal(32,2) DEFAULT NULL COMMENT '金额',
  `payment_status` varchar(64) DEFAULT NULL COMMENT '支付状态',
  `pay_type` varchar(16) DEFAULT NULL COMMENT '支付方式',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `project_id` varchar(32) DEFAULT NULL COMMENT '楼盘',
  `building_id` varchar(32) DEFAULT NULL COMMENT '楼栋',
  `room_id` varchar(64) DEFAULT NULL COMMENT '房间id',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='账单支付基础信息';

SET FOREIGN_KEY_CHECKS = 1;
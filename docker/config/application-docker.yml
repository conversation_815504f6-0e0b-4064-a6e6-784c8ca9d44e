server:
  port: 8080

spring:
  datasource:
    url: ****************************************************************************************************************************************************************************************
    username: prod_kip_billing
    password: prod_kip_billing123
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 25
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariDataSource-pool
      max-lifetime: 1800000
      connection-timeout: 120000
      connection-test-query: select 1
    jde:
      url: '************************************'
      username: 'JDEPRD'
      password: 'Oracle123'
      driver-class-name: oracle.jdbc.OracleDriver
      type: com.zaxxer.hikari.HikariDataSource
      hikari:
        minimum-idle: 6
        maximum-pool-size: 25
        idle-timeout: 30000
        pool-name: Jde-HikariDataSource-pool
        max-lifetime: 1800000
        connection-timeout: 120000
  redis:
    host: redis
    port: 6379
    database: 0
    timeout: 5s
    lettuce:
      pool:
        max-active: 50
        max-idle: 20
        min-idle: 10
        max-wait: 5s
      shutdown-timeout: 200ms
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 0
      max-request-size: 50MB
      max-file-size: 50MB
  jpa:
    database: mysql
    show-sql: true
    properties:
      hibernate:
        jdbc:
          batch_size: 500
        order_inserts: true
        order_updates: true
  thymeleaf:
    cache: false
    prefix: 'classpath:/templates/'
    encoding: UTF-8
    mode: HTML
    suffix: '.html'

mybatis:
  configuration:
    map-underscore-to-camel-case: true

logging:
  config: classpath:log4j2.xml
  level:
    com:
      kerryprops:
        kip:
          service:
            accelerator:
              feign: debug

feign:
  hystrix:
    enabled: true
  client:
    enabled: true
    config:
      default:
        connectTimeout: 30000
        readTimeout: 30000
        logger-level: BASIC

hystrix:
  threadPool:
    default:
      coreSize: 50
  command:
    default:
      execution:
        isolation:
          strategy: SEMAPHORE
          thread:
            timeoutInMilliseconds: 30000

# 项目相关配置
kerry:
  name: KerryLinks
  version: 2.3.0
  copyrightYear: 2019
  demoEnabled: false
  profile: /tmp/uploadPath
  addressEnabled: false
  userNameEmailSendMethod: 1
  projectRootUrl: http://localhost:9090/vue/

jde:
  dataBase: DATATRANSFER4
  viewName: VCONTRACT
  billTable: PRODDTA.F5515014
  F00022: PRODDTA.F00022
  F00092: PRODDTA.F00092
  jdeBillInvoiceOrigin: PRODDTA.F55GT01K
  jdeBillInvoiceRead: PRODDTA.F55GT02
  jdeBillInvoiceWriteBack: PRODDTA.F58Q9002

# 本地开发环境的外部服务配置（使用mock或跳过）
application:
  services:
    hiveAs: http://localhost:8080/mock
    bUser: http://localhost:8080/mock
    cUser: http://localhost:8080/mock
    sUser: http://localhost:8080/mock
    message: http://localhost:8080/mock
    messageCenter: http://localhost:8080/mock
    file: http://localhost:8080/mock
    auth: http://localhost:8080/mock
    support: http://localhost:8080/mock
    klClient: http://localhost:8080/mock
    staff: http://localhost:8080/mock
    kipInvoice: http://localhost:8080/mock
    unifiedMessage: http://localhost:8080/mock
    hiveService: http://localhost:8080/mock

kip:
  invoice:
    system-id: test-system-id
    system-secret: test-system-secret
    msg-callback:
      email: http://localhost:8080/bill/invoice/email/callback
      sms: http://localhost:8080/bill/invoice/sms/callback
  payment:
    host: http://localhost:8080/mock/services
    keystorePath: classpath:/store_client.jks
    keystorePassword: password003
    aliasName: pmw_client
    keyPassword: password002
    connectionTimeout: 3000
    pmwPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsUoakaogSefZWK5lH70SrFV3xtyGsUGGyMuUj3XkWbWtziSN3QYBkDeo+FHGANNLWbV7i0hVsHC7cHjVZ/OOe4YdCF6KlpNTWfEjSJkfrxFtyoJhpW7PPXYu9HvCRstZ65L5j0EhSK2bg5KlkY4oBv9XEzOiDNe3wnxvqQCd6Q+pQybQpTIJLFNQppoyqgSzNTCtxceZbR+OY6xwA3b1OesvcksutZtp6XkVsvUiClYIaVWnyE14bmoROQPNskeURVFOSu/ITquWHANINNHF3bQ8HPsdBvtyPt4Rug2zuSKKhnpcDpbnqjZY4a7F0wQkGLPf0LdkvgqnG2T+g/GuyQIDAQAB
    ttl: 15
    feePayTtl: 48h
    notifyUrl: http://localhost:8080/c/apt/pay/callback
    directDebitsPayTtl: 10

# 定时任务配置（本地开发环境禁用）
scheduler:
  scheduleBBill: "-"
  scheduleEFapiaoBBill: "-"
  fixEmailStatus: "-"
  scheduleCBill: "-"
  scheduleWriterPaytoJde: "-"
  confirmFromJde: "-"
  scheduleCBillHive: "-"
  conductDirectPayment: "-"
  scheduleSyncBillSendConfigMcus: "-"
  scheduleSyncEnterpriseAccounts: "-"

management:
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: /actuator
  endpoint:
    health:
      show-details: always
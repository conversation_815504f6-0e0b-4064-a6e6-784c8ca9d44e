{"permissions": {"allow": ["<PERSON><PERSON>(mvn test:*)", "<PERSON><PERSON>(mv:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(find:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(redis-cli:*)", "Bash(rm:*)", "Bash(grep:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mysql:*)", "<PERSON><PERSON>(open:*)", "Bash(ls:*)", "Bash(rg:*)", "Bash(git rm:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(docker rm:*)", "Bash(./docker-dev.sh:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker cp:*)", "Bash(jar:*)", "<PERSON><PERSON>(cat:*)", "Bash(timeout 60 mvn spring-boot:run 2 >& 1)", "<PERSON><PERSON>(pkill:*)"], "deny": []}}
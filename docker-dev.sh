#!/bin/bash

# KIP Billing 本地开发环境启动脚本

set -e

echo "=== KIP Billing Docker 本地开发环境 ==="
echo

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker 未运行，请先启动 Docker"
    exit 1
fi

# 检查命令行参数
case "${1:-start}" in
    "start")
        echo "🚀 启动 KIP Billing 基础开发环境..."
        echo "   - MySQL: localhost:3320"  
        echo "   - Oracle: localhost:1521"
        echo "   - Redis: localhost:6390"
        echo
        
        # 启动基础服务
        docker-compose up -d
        
        echo "⏳ 等待服务启动完成..."
        echo "   检查服务状态: docker-compose ps"
        echo "   查看服务日志: docker-compose logs -f"
        echo "   MySQL连接: mysql -h localhost -P 3320 -u prod_kip_billing -p"
        echo
        ;;
        
    "stop")
        echo "🛑 停止 KIP Billing 开发环境..."
        docker-compose down
        echo "✅ 所有服务已停止"
        ;;
        
    "restart")
        echo "🔄 重启 KIP Billing 开发环境..."
        docker-compose down
        docker-compose up -d
        echo "✅ 服务重启完成"
        ;;
        
    "logs")
        echo "📋 查看服务日志..."
        docker-compose logs -f
        ;;
        
    "status")
        echo "📊 服务状态："
        docker-compose ps
        ;;
        
    "clean")
        echo "🧹 清理 Docker 资源..."
        echo "警告：这将删除所有容器、镜像和数据卷"
        read -p "确认继续? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down -v --rmi all
            echo "✅ 清理完成"
        else
            echo "取消清理"
        fi
        ;;
        
    "pull")
        echo "🔄 拉取最新镜像..."
        docker-compose pull
        echo "✅ 镜像更新完成"
        ;;
        
    "help"|"--help"|"-h")
        echo "用法: $0 [命令]"
        echo
        echo "命令："
        echo "  start    启动开发环境 (默认)"
        echo "  stop     停止开发环境"
        echo "  restart  重启开发环境"
        echo "  logs     查看服务日志"
        echo "  status   查看服务状态"
        echo "  clean    清理所有Docker资源"
        echo "  pull     拉取最新镜像"
        echo "  help     显示此帮助信息"
        echo
        echo "服务地址："
        echo "  MySQL:   localhost:3320 (用户: prod_kip_billing, 密码: prod_kip_billing123)"
        echo "  Oracle:  localhost:1521 (用户: JDEPRD, 密码: Oracle123, 数据库: JDEPRD)"
        echo "  Redis:   localhost:6390"
        echo
        echo "提示："
        echo "  - 启动基础服务后，可在IDE中直接运行Spring Boot应用"
        echo "  - 应用配置文件已准备好连接这些服务"
        ;;
        
    *)
        echo "❌ 未知命令: $1"
        echo "使用 '$0 help' 查看可用命令"
        exit 1
        ;;
esac
{"request": {"method": "POST", "url": "/wx-subscribe/send", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"equalToJson": {"templateId": "0Fu42WnHW-P0poJ4tqWeo--96KCVzntIxDV6NI1vp4I", "openId": "o7w8q48a9BGrzxCbqVv4vOjuD1sE", "userId": "8aaa84cc8c86ba86018c8a3446f20000", "templateParams": {"thing2": "测试单元户号", "thing1": "测试小区", "thing5": "请及时缴纳费用，祝您生活愉快！", "thing3": "管理费"}}, "ignoreArrayOrder": true, "ignoreExtraElements": true}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json", "access-control-allow-credentials": "true", "access-control-allow-headers": "Content-Type,X-CAF-Authorization-Token,sessionToken,X-TOKEN,x-requested-with,access-control-allow-origin,XMLHttpRequest,requestToken,Authorization,timestamp,sign,user", "access-control-allow-methods": "GET, POST, OPTIONS, PUT, DELETE", "access-control-allow-origin": "*", "access-control-max-age": "3600", "x-conversation-id": "UMS-609-4261b0616faa", "x-correlation-id": "UMS-9a3-2042d3c3ec18"}, "jsonBody": {"code": "000000", "message": "成功", "data": true}}}
env:
  test

spring:
  data:
    jpa:
      repositories:
        bootstrap-mode: lazy
  datasource:
    url: jdbc:h2:mem:db_billing_service_test;DATABASE_TO_UPPER=false;MODE=MYSQL;DB_CLOSE_ON_EXIT=FALSE;
    username: sa
    password:
    driver-class-name: org.h2.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 25
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariDataSource-pool
      max-lifetime: 1800000
      connection-timeout: 120000
      connection-test-query: select 1
    jde:
      url: jdbc:h2:mem:db_billing_service_jde_test;DATABASE_TO_UPPER=false;MODE=Oracle;DB_CLOSE_ON_EXIT=FALSE;INIT=RUNSCRIPT FROM 'classpath:script/init_sql_oracle.sql';
      username: 'testUserName'
      password: 'testPassword'
      driver-class-name: org.h2.Driver
      type: com.zaxxer.hikari.HikariDataSource
      hikari:
        minimum-idle: 6
        maximum-pool-size: 25
        idle-timeout: 30000
        pool-name: Jde-HikariDataSource-pool
        max-lifetime: 1800000
        connection-timeout: 120000
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 0
      max-request-size: 50MB
      max-file-size: 50MB
  jpa:
    database: mysql
    show-sql: false
    open-in-view: false
    properties:
      hibernate:
        auto_quote_keyword: true
        jdbc:
          batch_size: 500
        order_inserts: true
        order_updates: true
    hibernate:
      ddl-auto: create
    # 移除 database-platform 配置，让 Hibernate 自动检测
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
mybatis:
  configuration:
    map-underscore-to-camel-case: true
logging:
  config: classpath:log4j2.xml
  level:
    # 最小化日志输出
    org.springframework: ERROR
    org.hibernate: ERROR
    org.apache: ERROR
    org.eclipse.jetty: ERROR
    com.zaxxer.hikari: ERROR
    com.github.tomakehurst.wiremock: ERROR
    org.zalando.logbook: OFF
    # 应用日志
    com.kerryprops.kip.bill.interceptors.BizAuthInterceptor: ERROR
    com.kerryprops.kip.bill.common.exceptions.handler.AppExceptionHandler: OFF
    com.kerryprops.kip.bill: ERROR
feign:
  hystrix:
    enabled: true
  client:
    enabled: true
    config:
      default:
        connectTimeout: 30000
        readTimeout: 30000
        logger-level: BASIC
hystrix:
  threadPool:
    default:
      coreSize: 50
  command:
    default:
      execution:
        isolation:
          strategy: SEMAPHORE
          thread:
            timeoutInMilliseconds: 30000
# 项目相关配置
kerry:
  # 名称
  name: KerryLinks
  # 版本
  version: 2.3.0
  # 版权年份
  copyrightYear: 2019
  # 实例演示开关
  demoEnabled: false
  # 文件路径 示例（ Windows配置D:/kerry/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /home/<USER>/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 进行发送账号密码的方式 0 按照已创建就进行发送，1按照task任务进行发送
  userNameEmailSendMethod: 1
  projectRootUrl: http://tsstest.kerryprops.com.cn:9090/vue/
  #关于空气质量需要进行重定向的 链接地址
  airReturnUrl: https://kerrylinks.kerryprops.com.cn/weather/Landscape?

jde:
  dataBase: DATATRANSFER4
  viewName: VCONTRACT
  an8Table: DATATRANSFER4.VEXTRAAN8J
  billTable: CRPDTA.F5515014
  siteView: V_REPAIR_WO_AB
  # jde派单同步接口
  publicDispatch: https://**************:82/WeixinMaster/GenerateWHFromWeiXinServlet.do
  companyDispatch: https://**************:82/WeixinMaster/WeixinMaster.do
  #同步jde评价的表
  F00022: CRPDTA.F00022
  F00092: CRPDTA.F00092
  #同步jde工单状态
  jdeDbPrefix: CRPDTA
  #同步jde工单费用
  jdeRepairBillingFee: DATATRANSFER8.WORKORDERFEE
  # 同步故障类型
  vwoFailtype: DATATRANSFER4.v_wo_failtype
  #同步jeb设备
  F1201: CRPDTA.F1201
  F1217: CRPDTA.F1217
  # e-fapiao：jde源数据库名
  jdeBillInvoiceOrigin: CRPDTA.F55GT01K
  # e-fapiao：jde已读标记数据库名
  jdeBillInvoiceRead: CRPDTA.F55GT02
  # e-fapiao：jde回写数据库名，记录开票完成的数据
  jdeBillInvoiceWriteBack: CRPDTA.F58Q9002
  #同步JDE公寓小区收款
  F560313: CRPDTA.F560313
  # JDE杂费信息相关表
  jdeCashierFee: CRPDTA.F550911M
# 文件上传服务相关配置
kerryfile:
  uploadUrl: https://kerrylinks.kerryprops.com.cn/kerryFileServer/files/upload
  loginUrl: https://kerrylinks.kerryprops.com.cn/kerryFileServer/user/login
  userName: kerry_links03
  password: 5J67N789f3hp5J90N
  oldFilePath: /usr/local/kencery/testTomcat/webapps/Renter/
  #多文件上传接口
  uploadFilesUrl: https://kerrylinks.kerryprops.com.cn/kerryFileServer/files/newUploadFiles
# 关闭 Logbook HTTP 日志记录
logbook:
  filter:
    form-request-mode: OFF
  write:
    level: OFF
  format:
    style: http
  exclude:
    - "/**"

wiremock:
  baseUrl: http://localhost:${wiremock.server.port}
application:
  services:
    hiveAs: https://dev-kip-service-internal.kerryonvip.com/hive-view-assembler-service
    bUser: https://dev-kip-service-internal.kerryonvip.com/tenant-admin-service
    cUser: https://dev-kip-service-internal.kerryonvip.com/profile-service
    sUser: https://dev-kip-service-internal.kerryonvip.com/kerry-staff-service
    message: ${wiremock.baseUrl}
    unifiedMessage: https://dev-kip-service-internal.kerryonvip.com/unified-messaging-service
    messageCenter: ${wiremock.baseUrl}
    file: https://dev-kip-service-internal.kerryonvip.com/file-service
    smart-travel: http://ks-smart-travel
    flexible-space: http://ks-flexible-space
    auth: https://dev-gateway-kip.kerryonvip.com
    support: https://dev-gateway-kip.kerryonvip.com
    klClient: https://kerrylinks.kerryprops.com.cn
    staff: https://dev-kip-service-internal.kerryonvip.com/kerry-staff-service
    kipInvoice: https://dev-kip-service-internal.kerryonvip.com/invoice-service
    hiveService: https://dev-kip-service-internal.kerryonvip.com/hive-service

kip:
  invoice:
    system-id: c4ada11d8d6d23df
    system-secret: 8a5014553b6049c23ac4da84d9dd8af5
  payment:
    host: https://dev-payment.kerryonvip.com/services
    keystorePath: classpath:/store_client.jks
    keystorePassword: password003
    aliasName: pmw_client
    keyPassword: password002
    connectionTimeout: 3000
    pmwPublicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsUoakaogSefZWK5lH70SrFV3xtyGsUGGyMuUj3XkWbWtziSN3QYBkDeo+FHGANNLWbV7i0hVsHC7cHjVZ/OOe4YdCF6KlpNTWfEjSJkfrxFtyoJhpW7PPXYu9HvCRstZ65L5j0EhSK2bg5KlkY4oBv9XEzOiDNe3wnxvqQCd6Q+pQybQpTIJLFNQppoyqgSzNTCtxceZbR+OY6xwA3b1OesvcksutZtp6XkVsvUiClYIaVWnyE14bmoROQPNskeURVFOSu/ITquWHANINNHF3bQ8HPsdBvtyPt4Rug2zuSKKhnpcDpbnqjZY4a7F0wQkGLPf0LdkvgqnG2T+g/GuyQIDAQAB
    ttl: 15
    notifyUrl: http://kip-billing/c/apt/pay/callback
    directDebitsAmountLimit: 0.1
    directDebitsPayTtl: 31
    directDebitsDailyUniqBatch: false
  signature:
    skcVc:
      system-id: kip4f3842c637732da5
      system-secret: kip8dfe013a0ad769bafef6bc0e66d001aa

server-auth:
  appCode:
    accept: accelerator
dm:
  wx:
    msgUrl: https://dev-consumer.kerryonvip.com/pages/package-profile/property-payment/index
    payFailedMsgUrl: https://dev-consumer.kerryonvip.com/pages/weixin-h5/index?page=billPayment&roomId=ROOM_ID
    paidMsgUrl: https://dev-consumer.kerryonvip.com/pages/package-profile/property-payment/order-detail?orderNo=ORDER_NO&pageType=record
  projects: 192,180,185,186,181,182,183,184,189,187,188,190,FZJLZX,NCHC
  cBillProjects: 192,181,FZHYF,FZJLZX,190,HNZZ,SYYSJ,SYYSDY,SYYSG,185,QHDHBT,HKPFYT
  kl:
    url: **********************************************
    username: root
    password: Kerry_links2021
scheduler:
  scheduleBBill: "-"
  scheduleEFapiaoBBill: "0 0 4 * * ?"
  fixEmailStatus: "-"
  scheduleCBill: "-"
  scheduleWriterPaytoJde: "-"
  confirmFromJde: "-"
  scheduleCBillHive: "-"
  scheduleSyncBillSendConfigMcus: "-"
  scheduleSyncEnterpriseAccounts: "-"
  conductDirectPayment: "-"

#  scheduleBBill: "0 0 0/1 * * ?"
#  scheduleCBill: "0 0 2 * * ?"
#  scheduleWriterPaytoJde: "0 0 3 * * ?"
#  confirmFromJde: "0 0 4 * * ?"

management:
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: /actuator
  endpoint:
    health:
      show-details: always

distributed:
  jobs:
    enabled: false

e-fapiao:
  sales-bill-type: JDE-Manual
  system-orig: JDE
  sales-bill-no-prefix: JDE
  sales-bill-no-suffix-len: 6
  purchaser-bank-account-stuff: "\u0020"
  blanks: "\u0020\u0020\u0020\u0020"
  upload-pause-short: 1000
  upload-pause-long: 10000
  co-valid-bu-period:
    - companyCode: 31007
      bus: ********,********,********
      startDate: 2019-01 # 格式：yyyy-MM
    - companyCode: 42010
      bus: ********,********,********
      startDate: 2019-08

full-digital:
  special-invoice-lease-goods-tax-prefix: *********

invoice-system-orig-manual-sales-bill-no-prefix: Z

springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false
    
bill:
  push:
    app-id: "wx92c3e55fbef6b2af"
    min-program-path: "pages/package-bill/redirectTo"
    template-id: "Z2aW5XguhdQ6tSZbox9m0Cnqq2XSqk_-5-bo8V2Kdb0"

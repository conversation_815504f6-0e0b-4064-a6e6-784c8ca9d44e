package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.webservice.vo.HealthResource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.actuate.health.CompositeHealthContributor;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthContributorRegistry;
import org.springframework.boot.actuate.health.HealthIndicator;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * HealthControllerTest
 *
 * <AUTHOR>
 * @date 2024-10-17
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("HealthController健康检查-单元测试")
class HealthControllerTest {

    @Mock
    private HealthContributorRegistry healthRegistry;

    @InjectMocks
    private HealthController healthController;

    @Test
    @DisplayName("健康检查，正常场景")
    void health_normalScenario() {
        // Arrange
        HealthIndicator dbHealthIndicator = mock(HealthIndicator.class);
        HealthIndicator jdeDBHealthIndicator = mock(HealthIndicator.class);
        HealthIndicator redisHealthIndicator = mock(HealthIndicator.class);

        CompositeHealthContributor dbContributor = mock(CompositeHealthContributor.class);

        when(healthRegistry.getContributor("db")).thenReturn(dbContributor);
        when(dbContributor.getContributor("dataSource")).thenReturn(dbHealthIndicator);
        when(dbContributor.getContributor("jdeDataSource")).thenReturn(jdeDBHealthIndicator);
        when(healthRegistry.getContributor("redis")).thenReturn(redisHealthIndicator);

        when(dbHealthIndicator.health()).thenReturn(Health.up().build());
        when(jdeDBHealthIndicator.health()).thenReturn(Health.up().build());
        when(redisHealthIndicator.health()).thenReturn(Health.up().build());

        // Act
        HealthResource result = healthController.health();

        // Assert
        assertEquals(HealthResource.SUCCESS_RESPONSE, result);
        verify(dbHealthIndicator, times(1)).health();
        verify(jdeDBHealthIndicator, times(1)).health();
        verify(redisHealthIndicator, times(1)).health();
    }


    @Test
    @DisplayName("健康检查，异常场景：抛出异常")
    void health_exceptionScenario() {
        // Arrange
        when(healthRegistry.getContributor("db")).thenThrow(new RuntimeException("Test exception"));

        // Act
        HealthResource result = healthController.health();

        // Assert
        assertEquals(HealthResource.FAIL_RESPONSE, result);
        verify(healthRegistry, times(1)).getContributor("db");
    }
}

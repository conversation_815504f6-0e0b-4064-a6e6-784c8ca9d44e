package com.kerryprops.kip.bill.webservice.scheduler;

import com.kerryprops.kip.bill.log4j.BSConversationFilter;
import com.kerryprops.kip.bill.webservice.impl.DirectDebitsBillBatchController;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * DirectDebitsSchedulerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @since - 2025-6-18
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("代扣定时任务-单元测试")
class DirectDebitsSchedulerTest {

    @Mock
    private DirectDebitsBillBatchController controller;

    @InjectMocks
    private DirectDebitsScheduler scheduler;

    @Test
    @DisplayName("执行代扣任务，正常场景")
    void conductDirectPayment_success() {
        // Act
        scheduler.conductDirectPayment();

        // Assert
        verify(controller, times(1)).conductDirectPayment();
    }

}
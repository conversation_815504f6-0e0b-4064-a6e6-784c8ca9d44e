package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.dao.entity.BillConfigEntity;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.impl.BillConfigServiceImpl;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigAddDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigListDto;
import com.kerryprops.kip.bill.webservice.vo.req.BillConfigUpdateDto;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * StaffAptBillControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-24
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("办公商场-账单配置-单元测试")
class AdminBillConfigControllerTest {

    @Mock
    private BillConfigServiceImpl billConfigService;

    @InjectMocks
    private AdminBillConfigController adminBillConfigController;

    @Test
    @DisplayName("获取账单配置详情，正常场景")
    void getBillConfig_success() {
        // Arrange
        Long id = 1L;
        BillConfigEntity mockEntity = new BillConfigEntity();
        mockEntity.setId(id);
        when(billConfigService.getBillConfig(id)).thenReturn(mockEntity);

        try (MockedStatic<UserInfoUtils> mockedStatic = mockStatic(UserInfoUtils.class)) {
            mockedStatic.when(UserInfoUtils::isSuperAdmin).thenReturn(true);

            // Act
            BillConfigEntity result = adminBillConfigController.getBillConfig(id);

            // Assert
            assertNotNull(result);
            assertEquals(id, result.getId());
            verify(billConfigService, times(1)).getBillConfig(id);
        }
    }

    @Test
    @DisplayName("获取账单配置详情，异常场景，非管理员")
    void getBillConfig_validAdminFailError() {
        // Arrange
        Long id = 1L;
        try (MockedStatic<UserInfoUtils> mockedStatic = mockStatic(UserInfoUtils.class)) {
            mockedStatic.when(UserInfoUtils::isSuperAdmin).thenReturn(false);

            // Act and Assert
            assertThrows(AppException.class, () -> adminBillConfigController.getBillConfig(id));
        }
    }

    @Test
    @DisplayName("获取账单配置详情，异常场景：非管理员访问")
    void getBillConfig_notAdmin() {
        // Arrange
        Long id = 1L;

        try (MockedStatic<UserInfoUtils> mockedStatic = mockStatic(UserInfoUtils.class)) {
            mockedStatic.when(UserInfoUtils::isSuperAdmin).thenReturn(false);

            // Act & Assert
            assertThrows(AppException.class, () -> adminBillConfigController.getBillConfig(id));
            verify(billConfigService, never()).getBillConfig(anyLong());
        }
    }

    @Test
    @DisplayName("查询账单配置列表，正常场景")
    void listBillConfigs_success() {
        // Arrange
        var queryDto = new BillConfigListDto();
        BillConfigEntity mockEntity = new BillConfigEntity();
        when(billConfigService.listBillConfigs(queryDto)).thenReturn(List.of(mockEntity));

        try (MockedStatic<UserInfoUtils> mockedStatic = mockStatic(UserInfoUtils.class)) {
            mockedStatic.when(UserInfoUtils::isSuperAdmin).thenReturn(true);

            // Act
            List<BillConfigEntity> result = adminBillConfigController.listBillConfigs(queryDto);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            verify(billConfigService, times(1)).listBillConfigs(queryDto);
        }
    }

    @Test
    @DisplayName("新增账单配置，正常场景")
    void addBillConfig_success() {
        // Arrange
        var addDto = new BillConfigAddDto();
        addDto.setSourceType("testSourceType");
        BillConfigEntity mockEntity = new BillConfigEntity();
        mockEntity.setSourceType("testSourceType");
        when(billConfigService.addBillConfig(addDto)).thenReturn(mockEntity);

        try (MockedStatic<UserInfoUtils> mockedStatic = mockStatic(UserInfoUtils.class)) {
            mockedStatic.when(UserInfoUtils::isSuperAdmin).thenReturn(true);

            // Act
            BillConfigEntity result = adminBillConfigController.addBillConfig(addDto);

            // Assert
            assertNotNull(result);
            assertEquals("testSourceType", result.getSourceType());
            verify(billConfigService, times(1)).addBillConfig(addDto);
        }
    }

    @Test
    @DisplayName("更新账单配置，正常场景")
    void updateBillConfig_success() {
        // Arrange
        Long id = 1L;
        var updateDto = new BillConfigUpdateDto();
        updateDto.setSourceType("updatedSourceType");
        BillConfigEntity mockEntity = new BillConfigEntity();
        mockEntity.setSourceType("updatedSourceType");
        when(billConfigService.updateBillConfig(id, updateDto)).thenReturn(mockEntity);

        try (MockedStatic<UserInfoUtils> mockedStatic = mockStatic(UserInfoUtils.class)) {
            mockedStatic.when(UserInfoUtils::isSuperAdmin).thenReturn(true);

            // Act
            BillConfigEntity result = adminBillConfigController.updateBillConfig(id, updateDto);

            // Assert
            assertNotNull(result);
            assertEquals("updatedSourceType", result.getSourceType());
            verify(billConfigService, times(1)).updateBillConfig(id, updateDto);
        }
    }

    @Test
    @DisplayName("删除账单配置，正常场景")
    void deleteBillConfig_success() {
        // Arrange
        Long id = 1L;
        BillConfigEntity mockEntity = new BillConfigEntity();
        mockEntity.setId(id);
        when(billConfigService.deleteBillConfigById(id)).thenReturn(mockEntity);

        try (MockedStatic<UserInfoUtils> mockedStatic = mockStatic(UserInfoUtils.class)) {
            mockedStatic.when(UserInfoUtils::isSuperAdmin).thenReturn(true);

            // Act
            BillConfigEntity result = adminBillConfigController.deleteBillConfig(id);

            // Assert
            assertNotNull(result);
            assertEquals(id, result.getId());
            verify(billConfigService, times(1)).deleteBillConfigById(id);
        }
    }

}
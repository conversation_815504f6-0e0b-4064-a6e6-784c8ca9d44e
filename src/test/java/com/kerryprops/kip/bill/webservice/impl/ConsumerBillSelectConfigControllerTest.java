package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.BillSelectMode;
import com.kerryprops.kip.bill.service.impl.BillSelectConfigService;
import com.kerryprops.kip.bill.webservice.vo.req.BillSelectModeDto;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ConsumerBillSelectConfigControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @since - 2025-6-17
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("公寓小区-账单-单元测试")
class ConsumerBillSelectConfigControllerTest {

    @Mock
    private BillSelectConfigService billSelectConfigService;

    @InjectMocks
    private ConsumerBillSelectConfigController consumerBillSelectConfigController;

    @Test
    @DisplayName("查询指定单元的账单选择规则，正常场景")
    void getMode_success() {
        // Arrange
        BillSelectModeDto dto = new BillSelectModeDto();
        dto.setProjectId("192");
        dto.setBuildingId("Building1");
        dto.setRoomId("Room1");
        when(billSelectConfigService.getBillSelectMode(anyString(), anyString(), anyString())).thenReturn(BillSelectMode.ANY_BILLS);

        // Act
        ConsumerBillSelectConfigController.ModeVo result = consumerBillSelectConfigController.getMode(dto);

        // Assert
        assertNotNull(result);
        verify(billSelectConfigService, times(1)).getBillSelectMode(anyString(), anyString(), anyString());
    }


}
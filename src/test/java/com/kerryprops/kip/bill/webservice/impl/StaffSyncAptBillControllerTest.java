package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.service.impl.AptBillService;
import com.querydsl.core.types.Predicate;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

/**
 * StaffSyncAptBillControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-6-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("关于小区-同步账单controller-单元测试")
class StaffSyncAptBillControllerTest {

    @Mock
    private AptBillService billService;

    @Mock
    private AptBillRepository billRepository;

    @InjectMocks
    private StaffSyncAptBillController staffSyncAptBillController;

    @Test
    @DisplayName("同步账单，正常场景")
    void sync_NormalScenario() {
        // Arrange
        String projectId = "testProject";

        // Act
        RespWrapVo<Boolean> result = staffSyncAptBillController.sync(projectId);

        // Assert
        assertEquals(true, result.getData());
        verify(billService, times(1)).syncAptBill(projectId);
    }

    @Test
    @DisplayName("同步Hive账单，正常场景")
    void syncHive_NormalScenario() {
        // Act
        RespWrapVo<Boolean> result = staffSyncAptBillController.syncHive();

        // Assert
        assertEquals(true, result.getData());
        verify(billService, times(1)).syncHive();
    }

    @Test
    @DisplayName("验证用户，正常场景")
    void verifyUser_NormalScenario() {
        // Arrange
        String roomId = "room123";
        String billNo = "bill456";

        var aptBill = new AptBill();
        aptBill.setRoomId(roomId);
        aptBill.setAn8(billNo);

        when(billRepository.findAll(any(Predicate.class))).thenReturn(List.of(aptBill));

        // Act
        RespWrapVo<Boolean> result = staffSyncAptBillController.verifyUser(roomId, billNo);

        // Assert
        assertEquals(true, result.getData());
        verify(billRepository, times(1)).findAll(any(Predicate.class));
    }

    @Test
    @DisplayName("验证用户，异常场景：roomId或billNo为空")
    void verifyUser_EmptyRoomIdOrBillNo_ShouldThrowException() {
        // Arrange
        String roomId = "";
        String billNo = "bill456";

        // Act & Assert
        assertThrows(RuntimeException.class, () -> staffSyncAptBillController.verifyUser(roomId, billNo));
        verifyNoInteractions(billRepository);
    }

    @Test
    @DisplayName("验证用户，异常场景：账单不存在")
    void verifyUser_BillNotFound_ShouldReturnFalse() {
        // Arrange
        String roomId = "room123";
        String billNo = "bill456";

        when(billRepository.findAll(any(Predicate.class))).thenReturn(Collections.emptyList());

        // Act
        RespWrapVo<Boolean> result = staffSyncAptBillController.verifyUser(roomId, billNo);

        // Assert
        assertEquals(false, result.getData());
        verify(billRepository, times(1)).findAll(any(Predicate.class));
    }

}
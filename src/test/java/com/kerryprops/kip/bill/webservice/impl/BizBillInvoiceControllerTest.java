package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.service.KerryBillEFapiaoService;
import com.kerryprops.kip.bill.service.model.s.BizBillEFapiaoSearchReqBo;
import com.kerryprops.kip.bill.webservice.vo.req.BizBillInvoiceSearchRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.BizBillInvoiceResource;
import com.kerryprops.kip.bill.webservice.vo.resp.ContentUnreadInfoVo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * BizBillInvoiceControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-6-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("办公商场-B端发票管理-单元测试")
class BizBillInvoiceControllerTest {

    @Mock
    private KerryBillEFapiaoService kerryBillEFapiaoService;

    @InjectMocks
    private BizBillInvoiceController bizBillInvoiceController;

    @Test
    @DisplayName("按条件查询，正常场景")
    void userInvoiceList_NormalScenario() {
        // Arrange
        BizBillInvoiceSearchRequest request = new BizBillInvoiceSearchRequest();
        Page<BizBillInvoiceResource> mockPage = mock(Page.class);
        when(kerryBillEFapiaoService.userEFapiaoList(any(BizBillEFapiaoSearchReqBo.class), any(Pageable.class)))
                .thenReturn(mockPage);

        try (MockedStatic<BeanUtil> mockedStatic = mockStatic(BeanUtil.class)) {
            mockedStatic.when(() -> BeanUtil.copy(any(), eq(BizBillEFapiaoSearchReqBo.class)))
                        .thenReturn(new BizBillEFapiaoSearchReqBo());

            // Act
            Page<BizBillInvoiceResource> result = bizBillInvoiceController.userInvoiceList(Pageable.unpaged(), request);

            // Assert
            assertEquals(mockPage, result);
            verify(kerryBillEFapiaoService, times(1)).userEFapiaoList(any(BizBillEFapiaoSearchReqBo.class), any(Pageable.class));
        }
    }

    @Test
    @DisplayName("查询发票URL，正常场景")
    void invoiceUrl_NormalScenario() {
        // Arrange
        long id = 123L;
        String expectedUrl = "http://example.com/invoice/123";
        when(kerryBillEFapiaoService.queryUrlsByBillInvoiceId(id)).thenReturn(expectedUrl);

        // Act
        String result = bizBillInvoiceController.invoiceUrl(id);

        // Assert
        assertEquals(expectedUrl, result);
        verify(kerryBillEFapiaoService, times(1)).queryUrlsByBillInvoiceId(id);
    }

    @Test
    @DisplayName("当前用户是否全部已读，正常场景")
    void userInvoiceReadStatus_NormalScenario() {
        // Arrange
        var unreadInfoVo = new ContentUnreadInfoVo();
        when(kerryBillEFapiaoService.userInvoiceReadStatus()).thenReturn(unreadInfoVo);

        // Act
        ContentUnreadInfoVo result = bizBillInvoiceController.userInvoiceReadStatus();

        // Assert
        verify(kerryBillEFapiaoService, times(1)).userInvoiceReadStatus();
    }

    @Test
    @DisplayName("标记发票信息当前用户已读，正常场景")
    void setUserInvoiceReaded_NormalScenario() {
        // Arrange
        Long id = 123L;
        when(kerryBillEFapiaoService.setUserInvoiceReaded(id)).thenReturn(true);

        // Act
        Boolean result = bizBillInvoiceController.setUserInvoiceReaded(id);

        // Assert
        assertEquals(true, result);
        verify(kerryBillEFapiaoService, times(1)).setUserInvoiceReaded(id);
    }

    @Test
    @DisplayName("标记发票信息当前用户全部已读，正常场景")
    void setUserInvoiceAllReaded_NormalScenario() {
        // Arrange
        when(kerryBillEFapiaoService.setUserInvoiceAllReaded()).thenReturn(true);

        // Act
        Boolean result = bizBillInvoiceController.setUserInvoiceAllReaded();

        // Assert
        assertEquals(true, result);
        verify(kerryBillEFapiaoService, times(1)).setUserInvoiceAllReaded();
    }
}
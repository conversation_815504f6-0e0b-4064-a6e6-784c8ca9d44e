package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.jde.OracleSql;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.vo.req.AptOtherPayConfigSaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptOtherPayConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigSaveVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptPayConfigUpdateVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayConfigVo;
import com.kerryprops.kip.hiveas.feign.dto.resp.ProjectRespDto;
import com.kerryprops.kip.hiveas.webservice.resource.resp.ProjectResp;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * StaffAptPayConfigControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("C端支付配置Controller-单元测试")
class StaffAptPayConfigControllerTest {

    @Mock
    private HiveAsClient hiveAsClient;

    @Mock
    private AptPayRepository payRepository;

    @Mock
    private AptPayConfigRepository aptPayConfigRepository;

    @InjectMocks
    private StaffAptPayConfigController staffAptPayConfigController;

    @Test
    @DisplayName("save - 异常场景：付费方式已存在")
    void save_paymentTypeExists() {
        // Arrange
        AptPayConfigSaveVo saveReqVo = new AptPayConfigSaveVo();
        saveReqVo.setProjectId("192");
        saveReqVo.setPaymentType("微信支付");
        saveReqVo.setCompanyCode("31007");

        AptPayConfig existingConfig = new AptPayConfig();
        when(aptPayConfigRepository.findAll(any(BooleanExpression.class))).thenReturn(List.of(existingConfig));

        // Act & Assert
        assertThrows(AppException.class, () -> staffAptPayConfigController.save(saveReqVo));
        verify(aptPayConfigRepository, never()).save(any(AptPayConfig.class));
    }

    @Test
    @DisplayName("update - 异常场景：配置不存在")
    void update_configNotFound() {
        // Arrange
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            AptPayConfigUpdateVo updateReqVo = new AptPayConfigUpdateVo();
            updateReqVo.setId(1L);

            when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.empty());

            // Act & Assert
            assertThrows(RuntimeException.class, () -> staffAptPayConfigController.update(updateReqVo));
            verify(aptPayConfigRepository, never()).save(any(AptPayConfig.class));
        }
    }

    @Test
    @DisplayName("update - 异常场景：hive数据不存在")
    void update_hiveDataNotFound() {
        // Arrange
        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());
            AptPayConfigUpdateVo updateReqVo = new AptPayConfigUpdateVo();
            updateReqVo.setId(1L);
            updateReqVo.setProjectId("111");

            AptPayConfig existingConfig = new AptPayConfig();
            existingConfig.setProjectId("222"); // 不同的楼盘ID

            when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(existingConfig));

            // Act & Assert
            assertThrows(RuntimeException.class, () -> staffAptPayConfigController.update(updateReqVo));
            verify(aptPayConfigRepository, never()).save(any(AptPayConfig.class));
        }
    }

    @Test
    @DisplayName("更新支付配置，正常场景")
    void update_success() {
        // Arrange
        AptPayConfigUpdateVo updateReqVo = new AptPayConfigUpdateVo();
        updateReqVo.setId(1L);
        updateReqVo.setProjectId("192");

        AptPayConfig existingConfig = new AptPayConfig();
        existingConfig.setId(1L);
        existingConfig.setProjectId("111");
        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(existingConfig));

        var projectRespDto = new ProjectRespDto();
        projectRespDto.setName("Test Project");
        var projectResp = new ProjectResp();
        projectResp.setProject(projectRespDto);
        RespWrapVo<ProjectResp> respRespWrapVo = new RespWrapVo<>(projectResp);
        when(hiveAsClient.getProjectById(any())).thenReturn(respRespWrapVo);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any())).thenAnswer(invocation -> {
                Object target = invocation.getArgument(1);
                return target;
            });
            mockedUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            RespWrapVo<Boolean> result = staffAptPayConfigController.update(updateReqVo);

            // Assert
            assertNotNull(result);
            assertTrue(result.getData());
            verify(aptPayConfigRepository, times(1)).save(existingConfig);
        }
    }

    @Test
    @DisplayName("saveOtherPay - 正常场景：保存成功")
    void saveOtherPay_success() {
        // Arrange
        var saveReqVo = new AptOtherPayConfigSaveVo();
        saveReqVo.setProjectId("192");
        saveReqVo.setPaymentType("微信支付");

        var projectRespDto = new ProjectRespDto();
        projectRespDto.setName("Test Project");
        var projectResp = new ProjectResp();
        projectResp.setProject(projectRespDto);
        RespWrapVo<ProjectResp> respRespWrapVo = new RespWrapVo<>(projectResp);
        when(hiveAsClient.getProjectById(any())).thenReturn(respRespWrapVo);

        when(aptPayConfigRepository.findAll(any(BooleanExpression.class)))
                .thenReturn(Collections.emptyList());

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class)) {
            var aptPayConfigSaveVo = new AptPayConfigSaveVo();
            aptPayConfigSaveVo.setProjectId("192");
            aptPayConfigSaveVo.setPaymentType("微信支付");
            aptPayConfigSaveVo.setCompanyCode("31006");
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(AptOtherPayConfigSaveVo.class), any()))
                                .thenReturn(aptPayConfigSaveVo);


            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(AptPayConfigSaveVo.class), any()))
                                .thenReturn(new AptPayConfig());

            // Act
            RespWrapVo<Boolean> respWrapVo = staffAptPayConfigController.saveOtherPay(saveReqVo);

            // Assert
            assertTrue(respWrapVo.getData());
        }
    }

    @Test
    @DisplayName("updateOtherPay - 正常场景：更新成功")
    void updateOtherPay_success() {
        // Arrange
        var updateReqVo = new AptOtherPayConfigUpdateVo();
        updateReqVo.setId(1L);
        updateReqVo.setProjectId("192");
        updateReqVo.setPaymentType("支付宝支付");

        AptPayConfig existingConfig = new AptPayConfig();
        existingConfig.setId(1L);
        existingConfig.setProjectId("111");
        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(existingConfig));

        var projectRespDto = new ProjectRespDto();
        projectRespDto.setName("Test Project");
        var projectResp = new ProjectResp();
        projectResp.setProject(projectRespDto);
        RespWrapVo<ProjectResp> respRespWrapVo = new RespWrapVo<>(projectResp);
        when(hiveAsClient.getProjectById(any())).thenReturn(respRespWrapVo);

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(AptOtherPayConfigUpdateVo.class), any()))
                                .thenReturn(new AptPayConfigUpdateVo());
            mockedUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            RespWrapVo<Boolean> respWrapVo = staffAptPayConfigController.updateOtherPay(updateReqVo);

            // Assert
            assertNotNull(respWrapVo);
            assertTrue(respWrapVo.getData());
            verify(aptPayConfigRepository, times(1)).save(existingConfig);
        }
    }

    @Test
    @DisplayName("删除支付配置，正常场景")
    void deleteById_success() {
        // Arrange
        Long id = 1L;
        AptPayConfig aptPayConfig = new AptPayConfig();
        aptPayConfig.setId(id);

        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPayConfig));

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(randomLoginUser());
            // Act
            RespWrapVo<Boolean> result = staffAptPayConfigController.deleteById(id);

            // Assert
            assertNotNull(result);
            assertTrue(result.getData());
            verify(aptPayConfigRepository, times(1)).deleteById(id);
        }
    }

    @Test
    @DisplayName("删除支付配置，异常场景：配置不存在")
    void deleteById_configNotFound() {
        // Arrange
        Long id = 1L;
        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.empty());

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(randomLoginUser());
            // Act
            RespWrapVo<Boolean> result = staffAptPayConfigController.deleteById(id);

            // Assert
            assertNotNull(result);
            assertFalse(result.getData());
            verify(aptPayConfigRepository, never()).deleteById(any());
        }
    }

    @Test
    @DisplayName("删除支付配置，异常场景：配置绑定支付数据")
    void deleteById_configBoundToPayments() {
        // Arrange
        Long id = 1L;
        AptPayConfig aptPayConfig = new AptPayConfig();
        aptPayConfig.setId(id);

        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPayConfig));
        when(payRepository.findAll(any(Predicate.class))).thenReturn(List.of(new AptPay()));
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act & Assert
            assertThrows(AppException.class, () -> staffAptPayConfigController.deleteById(id));
        }
    }

    @Test
    @DisplayName("查询支付配置，正常场景")
    void getById_success() {
        // Arrange
        Long id = 1L;
        AptPayConfig aptPayConfig = new AptPayConfig();
        aptPayConfig.setId(id);
        aptPayConfig.setProjectId("192");
        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPayConfig));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any())).thenReturn(new AptPayConfigVo());
            mockedUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            RespWrapVo<AptPayConfigVo> result = staffAptPayConfigController.getById(id);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getData());
            verify(aptPayConfigRepository, times(1)).findOne(any(Predicate.class));
        }
    }

    @Test
    @DisplayName("查询支付配置，异常场景：配置不存在")
    void getById_notFound() {
        // Arrange
        Long id = 1L;
        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.empty());

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(randomLoginUser());

            // Act
            RespWrapVo<AptPayConfigVo> result = staffAptPayConfigController.getById(id);

            // Assert
            assertNotNull(result);
            assertNull(result.getData());
            verify(aptPayConfigRepository, times(1)).findOne(any(Predicate.class));
        }
    }

    @Test
    @DisplayName("禁用支付配置，正常场景")
    void disableById_success() {
        // Arrange
        Long id = 1L;
        AptPayConfig aptPayConfig = new AptPayConfig();
        aptPayConfig.setId(id);
        aptPayConfig.setEnabledStatus(1);
        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPayConfig));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any())).thenReturn(new AptPayConfigVo());
            mockedUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            AptPayConfigVo result = staffAptPayConfigController.disableById(id);

            // Assert
            assertNotNull(result);
            assertEquals(0, aptPayConfig.getEnabledStatus());
            verify(aptPayConfigRepository, times(1)).save(aptPayConfig);
        }
    }

    @Test
    @DisplayName("禁用支付配置，异常场景：配置不存在")
    void disableById_notFound() {
        // Arrange
        Long id = 1L;
        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.empty());

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser)
                          .thenReturn(randomLoginUser());
            // Act and Assert
            assertThrows(AppException.class, () -> staffAptPayConfigController.disableById(id));
        }
    }

    @Test
    @DisplayName("启用支付配置，正常场景")
    void enableById_success() {
        // Arrange
        Long id = 1L;
        AptPayConfig aptPayConfig = new AptPayConfig();
        aptPayConfig.setId(id);
        aptPayConfig.setEnabledStatus(0);
        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.of(aptPayConfig));

        try (MockedStatic<BeanUtil> mockedStaticBeanUtil = Mockito.mockStatic(BeanUtil.class);
             MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticBeanUtil.when(() -> BeanUtil.copy(any(), any())).thenReturn(new AptPayConfigVo());
            mockedUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            AptPayConfigVo result = staffAptPayConfigController.enableById(id);

            // Assert
            assertNotNull(result);
            assertEquals(1, aptPayConfig.getEnabledStatus());
            verify(aptPayConfigRepository, times(1)).save(aptPayConfig);
        }
    }

    @Test
    @DisplayName("启用支付配置，异常场景：配置不存在")
    void enableById_notFound() {
        // Arrange
        Long id = 1L;
        when(aptPayConfigRepository.findOne(any(Predicate.class))).thenReturn(Optional.empty());

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act and Assert
            assertThrows(AppException.class, () -> staffAptPayConfigController.enableById(id));
        }
    }

    @Test
    @DisplayName("获取绑定范围，正常场景：超级管理员")
    void maxBindingScope_superAdmin() {
        // Arrange
        LoginUser mockUser = new LoginUser();
        mockUser.setRoles("SUPER_ADMIN");

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser).thenReturn(mockUser);

            // Act
            List<String> result = staffAptPayConfigController.maxBindingScope();

            // Assert
            assertNull(result);
            mockedUserInfo.verify(UserInfoUtils::getUser, times(1));
        }
    }

    @Test
    @DisplayName("获取绑定范围，正常场景：普通用户")
    void maxBindingScope_normalUser() {
        // Arrange
        LoginUser mockUser = new LoginUser();
        mockUser.setRoles("NOT_SUPER_ADMIN");
        mockUser.setProjectIds("Project1,Project2");

        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser).thenReturn(mockUser);

            // Act
            List<String> result = staffAptPayConfigController.maxBindingScope();

            // Assert
            assertNotNull(result);
            assertEquals(2, result.size());
            assertTrue(result.contains("Project1"));
            assertTrue(result.contains("Project2"));
            mockedUserInfo.verify(UserInfoUtils::getUser, times(1));
        }
    }

    @Test
    @DisplayName("获取绑定范围，异常场景：未登录")
    void maxBindingScope_notLoggedIn() {
        // Arrange
        try (MockedStatic<UserInfoUtils> mockedUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedUserInfo.when(UserInfoUtils::getUser).thenReturn(null);

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, () -> staffAptPayConfigController.maxBindingScope());
            assertEquals("not login.", exception.getMessage());
            mockedUserInfo.verify(UserInfoUtils::getUser, times(1));
        }
    }

}
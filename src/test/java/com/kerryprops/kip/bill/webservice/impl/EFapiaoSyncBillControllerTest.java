package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.config.EFapiaoConfig;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.service.EFapiaoBillService;
import com.kerryprops.kip.bill.service.EFapiaoJdeBillService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * EFapiaoSyncBillControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-6-17
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("办公商场-JDe同步账单到票易通-单元测试")
class EFapiaoSyncBillControllerTest {

    @Mock
    private EFapiaoConfig eFapiaoConfig;

    @Mock
    private EFapiaoJdeBillService eFapiaoJdeBillService;

    @Mock
    private EFapiaoBillService eFapiaoBillService;

    @InjectMocks
    private EFapiaoSyncBillController eFapiaoSyncBillController;

    @Test
    @DisplayName("补偿上传账单，正常场景")
    void compensateUpload_success() {
        var mockBill = EFapiaoJDEBill.builder()
                                     .build();
        when(eFapiaoJdeBillService.queryBySalesBillNo(anyString())).thenReturn(mockBill);

        // Act
        Object result = eFapiaoSyncBillController.compensateUpload("salesBillNo");

        // Assert
        assertNotNull(result);
        verify(eFapiaoJdeBillService, times(1)).queryBySalesBillNo(anyString());
        verify(eFapiaoBillService, times(1)).invoiceUpload(mockBill);
    }

    @Test
    @DisplayName("补偿上传账单，异常场景：账单不存在")
    void compensateUpload_billNotFound() {
        // Arrange
        String salesBillNo = "SALES123";
        when(eFapiaoJdeBillService.queryBySalesBillNo(salesBillNo)).thenReturn(null);

        // Act
        Object result = eFapiaoSyncBillController.compensateUpload(salesBillNo);

        // Assert
        assertNotNull(result);
        verify(eFapiaoJdeBillService, times(1)).queryBySalesBillNo(salesBillNo);
        verify(eFapiaoBillService, never()).invoiceUpload(any());
    }

    @Test
    @DisplayName("获取年月列表，异常场景：起始日期格式错误")
    void getYearMonths_invalidStartDate() {
        // Arrange
        String companyCode = "COMP123";
        String bus = "BUS123";
        String startDate = "2025";

        // Act & Assert
        assertThrows(RuntimeException.class, () -> ReflectionTestUtils.invokeMethod(eFapiaoSyncBillController,
                                                                                   "getYearMonths",
                                                                                  companyCode, bus,
                                                                                  startDate));
    }

    @Test
    @DisplayName("获取年月列表，正常场景")
    void getYearMonths_success() {
        // Arrange

        String companyCode = "COMP123";
        String bus = "BUS123";
        String startDate = "2025-01";

        when(eFapiaoJdeBillService.queryMaxDateByCompanyCodeAndBu(companyCode, bus)).thenReturn("2025-04");
        // Act
        List<String> yearMonths = (List<String>)ReflectionTestUtils.invokeMethod(eFapiaoSyncBillController, "getYearMonths",
                                                                                 companyCode, bus,
                                                                                 startDate);

        // Assert
        assertNotNull(yearMonths);
        assertEquals(4, yearMonths.size());
        verify(eFapiaoJdeBillService, times(1)).queryMaxDateByCompanyCodeAndBu(companyCode, bus);
    }

    @Test
    @DisplayName("获取年月列表，正常场景：最大日期为空")
    void getYearMonths_maxDateEmpty() {
        // Arrange
        String companyCode = "COMP123";
        String bus = "BUS123";
        String startDate = "2025-01";

        when(eFapiaoJdeBillService.queryMaxDateByCompanyCodeAndBu(companyCode, bus)).thenReturn(null);

        // Act
        List<String> yearMonths = (List<String>)ReflectionTestUtils.invokeMethod(eFapiaoSyncBillController, "getYearMonths",
                                                                                 companyCode, bus,
                                                                                 startDate);


        // Assert
        assertNotNull(yearMonths);
        assertFalse(yearMonths.isEmpty());
        verify(eFapiaoJdeBillService, times(1)).queryMaxDateByCompanyCodeAndBu(companyCode, bus);
    }

    @Test
    @DisplayName("同步账单，正常场景")
    void sync_normalScenario() {
        // Arrange
        EFapiaoConfig.Item item = new EFapiaoConfig.Item();
        item.setCompanyCode("COMP123");
        item.setBus("BUS123");
        item.setStartDate("2025-01");

        List<EFapiaoConfig.Item> items = List.of(item);
        when(eFapiaoConfig.getCoValidBuPeriod()).thenReturn(items);

        EFapiaoJDEBill bill = EFapiaoJDEBill.builder().build();
        when(eFapiaoJdeBillService.queryByCompanyCodeAndMonthAndBu("COMP123", "2025-01", "BUS123"))
                .thenReturn(List.of(bill, bill, bill, bill, bill, bill, bill, bill, bill, bill, bill));

        // Act
        Boolean result = eFapiaoSyncBillController.sync(item, true);

        // Assert
        assertTrue(result);
        verify(eFapiaoJdeBillService, times(1))
                .queryByCompanyCodeAndMonthAndBu("COMP123", "2025-01", "BUS123");
        verify(eFapiaoBillService, times(11)).invoiceUpload(bill);
    }


}
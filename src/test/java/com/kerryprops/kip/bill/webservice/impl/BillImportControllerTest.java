package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.service.KerryBillImportService;
import com.kerryprops.kip.bill.webservice.vo.req.ImportKerryBillRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.ImportedBillResource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BillImportControllerTest {

    @Mock
    private KerryBillImportService billImportService;

    @InjectMocks
    private BillImportController billImportController;

    @Test
    @DisplayName("导入账单，正常场景：导入成功")
    void billImport_Success() {
        // Arrange
        int mockedCount = 5;

        when(billImportService.importBill(any())).thenReturn(mockedCount);

        // Act
        ImportedBillResource response = billImportController.billImport(new ImportKerryBillRequest());

        // Assert
        assertEquals(mockedCount, response.getImportedBillCount());
    }

}
package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.service.CounterCashierService;
import com.kerryprops.kip.bill.webservice.vo.req.CashierAptPaySearchRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierOfflinePayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierQRCodePaymentRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierAptPaymentInfoResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierOfflinePayResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierPaymentReceiptResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierPaymentTransactionResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * CounterCashierControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-6-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("S端-收银台-单元测试")
class CounterCashierControllerTest {

    @Mock
    private CounterCashierService counterCashierService;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private CounterCashierController counterCashierController;

    @Test
    @DisplayName("离线支付-录入收款单，正常场景")
    void offlinePay4Cashier_NormalScenario() {
        // Arrange
        var request = new CashierOfflinePayRequest();
        request.setProjectId("testProject");
        request.setPayType("现金支付");

        var expectedResponse = new CashierOfflinePayResource();
        when(counterCashierService.offlinePay(any(CashierOfflinePayRequest.class))).thenReturn(expectedResponse);

        // Act
        CashierOfflinePayResource result = counterCashierController.offlinePay4Cashier(request);

        // Assert
        assertEquals(expectedResponse, result);
        verify(counterCashierService, times(1)).offlinePay(any(CashierOfflinePayRequest.class));
    }

    @Test
    @DisplayName("账单收款-扫码支付，正常场景")
    void counterCashierQRCodePayment_NormalScenario() {
        // Arrange
        var request = new CashierQRCodePaymentRequest();
        request.setProjectId("testProject");

        var expectedResponse = new CashierQRCodePaymentResource();

        when(counterCashierService.counterCashierQRCodePayment(any(CashierQRCodePaymentRequest.class))).thenReturn(
                expectedResponse);

        // Act
        CashierQRCodePaymentResource result = counterCashierController.counterCashierQRCodePayment(request);

        // Assert
        assertEquals(expectedResponse, result);
        verify(counterCashierService, times(1)).counterCashierQRCodePayment(any(CashierQRCodePaymentRequest.class));
    }

    @Test
    @DisplayName("收银台-查询支付详情，正常场景")
    void queryPaymentInfo_NormalScenario() {
        // Arrange
        String paymentInfoId = "testPaymentId";
        var expectedResponse = new CashierAptPaymentInfoResource();

        when(counterCashierService.queryCashierPaymentInfo(paymentInfoId)).thenReturn(expectedResponse);

        // Act
        CashierAptPaymentInfoResource result = counterCashierController.queryPaymentInfo(paymentInfoId);

        // Assert
        assertEquals(expectedResponse, result);
        verify(counterCashierService, times(1)).queryCashierPaymentInfo(paymentInfoId);
    }

    @Test
    @DisplayName("缴费记录详情-收据，正常场景")
    void acquirePaymentReceipt_NormalScenario() {
        // Arrange
        String paymentInfoId = "testPaymentId";
        CashierPaymentReceiptResource expectedResponse = new CashierPaymentReceiptResource();

        when(counterCashierService.acquirePaymentReceipt(paymentInfoId)).thenReturn(expectedResponse);

        // Act
        CashierPaymentReceiptResource result = counterCashierController.acquirePaymentReceipt(paymentInfoId);

        // Assert
        assertEquals(expectedResponse, result);
        verify(counterCashierService, times(1)).acquirePaymentReceipt(paymentInfoId);
    }

    @Test
    @DisplayName("账单收款-缴费记录，正常场景")
    void queryCashierAptPays_NormalScenario() {
        // Arrange
        var request = new CashierAptPaySearchRequest();
        Pageable pageable = mock(Pageable.class);
        Page<CashierPaymentTransactionResource> expectedResponse = mock(Page.class);

        when(counterCashierService.queryCashierAptPays(any(CashierAptPaySearchRequest.class),
                                                       any(Pageable.class))).thenReturn(expectedResponse);

        // Act
        Page<CashierPaymentTransactionResource> result =
                counterCashierController.queryCashierAptPays(request, pageable);

        // Assert
        assertEquals(expectedResponse, result);
        verify(counterCashierService, times(1)).queryCashierAptPays(any(CashierAptPaySearchRequest.class),
                                                                    any(Pageable.class));
    }

    @Test
    @DisplayName("账单收款-缴费记录-导出，正常场景")
    void exportCashierAptPays_NormalScenario() {
        // Arrange
        var request = new CashierAptPaySearchRequest();
        HttpServletResponse response = mock(HttpServletResponse.class);

        // Act
        counterCashierController.exportCashierAptPays(request, response);

        // Assert
        verify(counterCashierService, times(1)).exportCashierAptPays(any(CashierAptPaySearchRequest.class),
                                                                     any(HttpServletResponse.class));
    }

}
package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.service.IBillSendConfigService;
import com.kerryprops.kip.bill.webservice.vo.resp.BillPayer;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSendConfigResource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * StaffBillSendConfigControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON> @since - 2025-6-20
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("办公商场-发送配置-单元测试")
class StaffBillSendConfigControllerTest {

    @InjectMocks
    private StaffBillSendConfigController staffBillSendConfigController;

    @Mock
    private IBillSendConfigService configService;

    @Test
    @DisplayName("模糊查询付款人列表，正常场景")
    void fuzzyQueryPayers_normalScenario() {
        // Arrange
        BillPayer payer1 = new BillPayer();
        BillPayer payer2 = new BillPayer();
        when(configService.fuzzyQueryPayers(any(), any())).thenReturn(List.of(payer1, payer2));

        // Act
        List<BillPayer> result = staffBillSendConfigController.fuzzyQueryPayers("projectId", "query");

        // Assert
        assertEquals(2, result.size());
        verify(configService, times(1)).fuzzyQueryPayers(any(), any());
    }

    @Test
    void syncMcuFromTenantBills_normalScenario() {
        // Arrange
        when(configService.syncMcuFromTenantBills()).thenReturn(true);

        // Act
        RespWrapVo<Boolean> result = staffBillSendConfigController.syncMcuFromTenantBills();

        // Assert
        assertEquals(true, result.getData());
        verify(configService, times(1)).syncMcuFromTenantBills();
    }

    @Test
    @DisplayName("批量查询企业版账号，正常场景")
    void syncEnterpriseAccounts_normalScenario() {
        // Arrange
        when(configService.syncEnterpriseAccounts()).thenReturn(true);

        // Act
        RespWrapVo<Boolean> result = staffBillSendConfigController.syncEnterpriseAccounts();

        // Assert
        assertEquals(true, result.getData());
        verify(configService, times(1)).syncEnterpriseAccounts();
    }

    @Test
    @DisplayName("根据ID查询配置，正常场景")
    void queryConfig_normalScenario() {
        // Arrange
        long id = 1L;
        var resource = new BillSendConfigResource();
        resource.setId(id);
        when(configService.queryById(id)).thenReturn(resource);

        // Act
        BillSendConfigResource result = staffBillSendConfigController.queryConfig(id);

        // Assert
        assertEquals(id, result.getId());
        verify(configService, times(1)).queryById(id);
    }

}
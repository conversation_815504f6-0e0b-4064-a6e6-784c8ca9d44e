package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.service.CounterCashierFeeService;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePaysRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePaysVerifyRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeePrepayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierFeeQRCodePayRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayDetailResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePayResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierFeePaysResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * CounterCashierFeeControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-6-17
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("收银台-杂费-单元测试")
class CounterCashierFeeControllerTest {

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private CounterCashierFeeService counterCashierFeeService;

    @InjectMocks
    private CounterCashierFeeController counterCashierFeeController;

    @Test
    @DisplayName("杂费单-离线支付，正常场景")
    void feeOfflinePay_success() {
        // Arrange
        CashierFeePayRequest request = new CashierFeePayRequest();
        request.setPayType("现金支付");
        CashierFeePayResource mockResponse = new CashierFeePayResource();
        mockResponse.setPaymentInfoId("123");
        when(counterCashierFeeService.feeOfflinePay(request)).thenReturn(mockResponse);

        // Act
        CashierFeePayResource result = counterCashierFeeController.feeOfflinePay(request);

        // Assert
        assertNotNull(result);
        assertEquals("123", result.getPaymentInfoId());
        verify(counterCashierFeeService, times(1)).feeOfflinePay(request);
    }

    @Test
    @DisplayName("杂费单-预付款，正常场景")
    void prepay_success() {
        // Arrange
        CashierFeePrepayRequest request = new CashierFeePrepayRequest();
        CashierFeePayResource mockResponse = new CashierFeePayResource();
        mockResponse.setPaymentInfoId("456");
        when(counterCashierFeeService.prepay(request)).thenReturn(mockResponse);

        // Act
        CashierFeePayResource result = counterCashierFeeController.prepay(request);

        // Assert
        assertNotNull(result);
        assertEquals("456", result.getPaymentInfoId());
        verify(counterCashierFeeService, times(1)).prepay(request);
    }

    @Test
    @DisplayName("杂费单列表，正常场景")
    void queryCashierAptPays_success() {
        // Arrange
        CashierFeePaysRequest request = new CashierFeePaysRequest();
        Pageable pageable = PageRequest.of(0, 10);
        Page<CashierFeePaysResource> mockPage = new PageImpl<>(Collections.emptyList());
        when(counterCashierFeeService.queryFeePays(request, pageable)).thenReturn(mockPage);

        // Act
        Page<CashierFeePaysResource> result = counterCashierFeeController.queryCashierAptPays(request, pageable);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(counterCashierFeeService, times(1)).queryFeePays(request, pageable);
    }

    @Test
    @DisplayName("杂费单详情，正常场景")
    void queryFeePayDetail_success() {
        // Arrange
        String paymentInfoId = "789";
        CashierFeePayDetailResource mockResponse = new CashierFeePayDetailResource();
        mockResponse.setPaymentInfoId(paymentInfoId);
        when(counterCashierFeeService.queryFeePayDetail(paymentInfoId)).thenReturn(mockResponse);

        // Act
        CashierFeePayDetailResource result = counterCashierFeeController.queryFeePayDetail(paymentInfoId);

        // Assert
        assertNotNull(result);
        assertEquals(paymentInfoId, result.getPaymentInfoId());
        verify(counterCashierFeeService, times(1)).queryFeePayDetail(paymentInfoId);
    }

    @Test
    @DisplayName("杂费账单-审核/取消审核，正常场景")
    void switchFeePayVerifyStatus_success() {
        // Arrange
        Long id = 1L;
        CashierFeePayDetailResource mockResponse = new CashierFeePayDetailResource();
        mockResponse.setPaymentInfoId("123");
        when(counterCashierFeeService.switchFeePayVerifyStatus(id)).thenReturn(mockResponse);

        // Act
        CashierFeePayDetailResource result = counterCashierFeeController.switchFeePayVerifyStatus(id);

        // Assert
        assertNotNull(result);
        assertEquals("123", result.getPaymentInfoId());
        verify(counterCashierFeeService, times(1)).switchFeePayVerifyStatus(id);
    }

    @Test
    @DisplayName("杂费账单-取消，正常场景")
    void cancelFeePay_success() {
        // Arrange
        Long id = 2L;
        CashierFeePayDetailResource mockResponse = new CashierFeePayDetailResource();
        mockResponse.setPaymentInfoId("456");
        when(counterCashierFeeService.cancelFeePay(id)).thenReturn(mockResponse);

        // Act
        CashierFeePayDetailResource result = counterCashierFeeController.cancelFeePay(id);

        // Assert
        assertNotNull(result);
        assertEquals("456", result.getPaymentInfoId());
        verify(counterCashierFeeService, times(1)).cancelFeePay(id);
    }

    @Test
    @DisplayName("杂费账单-回写JDE，正常场景")
    void writeBackFee2JDE_success() {
        // Arrange
        String projectId = "PROJ_001";
        when(counterCashierFeeService.writeBackFee2JDE(projectId)).thenReturn(1);

        // Act
        Integer result = counterCashierFeeController.writeBackFee2JDE(projectId);

        // Assert
        assertNotNull(result);
        assertEquals(1, result);
        verify(counterCashierFeeService, times(1)).writeBackFee2JDE(projectId);
    }

    @Test
    @DisplayName("杂费单-扫码缴费，正常场景")
    void counterCashierFeeQRCodePayment_success() {
        // Arrange
        CashierFeeQRCodePayRequest request = new CashierFeeQRCodePayRequest();
        CashierQRCodePaymentResource mockResponse = new CashierQRCodePaymentResource();
        when(counterCashierFeeService.counterCashierQRCodePayment(request)).thenReturn(mockResponse);

        // Act
        CashierQRCodePaymentResource result = counterCashierFeeController.counterCashierFeeQRCodePayment(request);

        // Assert
        assertNotNull(result);
        verify(counterCashierFeeService, times(1)).counterCashierQRCodePayment(request);
    }

    @Test
    @DisplayName("杂费单列表-导出，正常场景")
    void exportCashierAptPays_success() {
        // Arrange
        var request = new CashierFeePaysRequest();
        var httpServletResponse = mock(HttpServletResponse.class);

        // Act
        counterCashierFeeController.exportCashierAptPays(request, httpServletResponse);

        // Assert
        verify(counterCashierFeeService, times(1)).exportFeePays(any(), any());
    }

    @Test
    @DisplayName("杂费账单-按条件审核，正常场景")
    void conditionalVerifyFeePay_success() {
        // Arrange
        var request = new CashierFeePaysVerifyRequest();
        CashierFeePayDetailResource mockResource = new CashierFeePayDetailResource();
        mockResource.setPaymentInfoId("123");
        when(counterCashierFeeService.conditionalVerifyFeePay(request)).thenReturn(Collections.singletonList(mockResource));

        // Act
        List<CashierFeePayDetailResource> result = counterCashierFeeController.conditionalVerifyFeePay(request);

        // Assert
        assertEquals(1, result.size());
        assertEquals("123", result.get(0).getPaymentInfoId());
        verify(counterCashierFeeService, times(1)).conditionalVerifyFeePay(request);
    }

}
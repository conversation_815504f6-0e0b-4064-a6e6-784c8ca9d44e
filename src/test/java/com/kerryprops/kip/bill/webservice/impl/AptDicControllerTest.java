package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.BillPayChannel;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.webservice.vo.resp.EnumResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * StaffAptBillControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @since - 2025-04-24
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("公寓小区-查询支付类型名称-单元测试")
class AptDicControllerTest {

    @InjectMocks
    private AptDicController aptDicController;

    @Test
    @DisplayName("获取C端枚举，正常场景")
    void getCEnums_NormalScenario() {
        // Arrange
        String name = null;

        // Act
        RespWrapVo<Map<String, List<EnumResponse>>> result = aptDicController.getCEnums(name);

        // Assert
        assertNotNull(result);
    }

    @Test
    @DisplayName("获取C端枚举，按名称过滤，正常场景")
    void getCEnums_FilterByName_NormalScenario() {
        // Arrange
        String name = "payChannel";

        // Act
        RespWrapVo<Map<String, List<EnumResponse>>> result = aptDicController.getCEnums(name);

        // Assert
        assertEquals(BillPayChannel.values().length, result.getData().get(name).size());
    }

    @Test
    @DisplayName("获取S端枚举，正常场景")
    void getSEnums_NormalScenario() {
        // Arrange
        String name = null;

        // Act
        RespWrapVo<Map<String, List<EnumResponse>>> result = aptDicController.getSEnums(name);

        // Assert
        assertEquals(BillPayChannel.values().length, result.getData().get("payChannel").size());
    }

    @Test
    @DisplayName("获取S端枚举，按名称过滤，正常场景")
    void getSEnums_FilterByName_NormalScenario() {
        // Arrange
        String name = "payChannel";

        // Act
        RespWrapVo<Map<String, List<EnumResponse>>> result = aptDicController.getSEnums(name);

        // Assert
        assertEquals(BillPayChannel.values().length, result.getData().get(name).size());
    }

}
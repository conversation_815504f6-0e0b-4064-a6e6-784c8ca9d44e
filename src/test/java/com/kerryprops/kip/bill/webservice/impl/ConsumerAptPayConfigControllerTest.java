package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.entity.QAptPayConfig;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * ConsumerAptPayConfigControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-6-16
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("公寓小区-查询支付类型-单元测试")
class ConsumerAptPayConfigControllerTest {

    @Mock
    private AptPayConfigRepository aptPayConfigRepository;

    @InjectMocks
    private ConsumerAptPayConfigController consumerAptPayConfigController;

    @Test
    @DisplayName("查询支付类型，正常场景")
    void queryPaymentType() {
        // Arrange
        List<String> mockPaymentTypes = List.of("Type1", "Type2");

        var mockQuery = mock(JPAQuery.class);
        var jpaQueryFactory = mock(JPAQueryFactory.class);

        when(aptPayConfigRepository.getJpaQueryFactory()).thenReturn(jpaQueryFactory);

        when(jpaQueryFactory.selectDistinct(eq(QAptPayConfig.aptPayConfig.paymentType))).thenReturn(mockQuery);
        when(mockQuery.from(any(EntityPath.class))).thenReturn(mockQuery);
        when(mockQuery.where(any(BooleanExpression.class))).thenReturn(mockQuery);
        when(mockQuery.fetch()).thenReturn(mockPaymentTypes);

        // Act
        RespWrapVo<List<String>> respWrapVo =
                consumerAptPayConfigController.queryPaymentType("projectId", "companyCode");

        // Assert
        assertNotNull(respWrapVo);
        assertEquals(mockPaymentTypes, respWrapVo.getData());
    }

}
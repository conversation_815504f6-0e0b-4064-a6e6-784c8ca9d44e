package com.kerryprops.kip.bill.webservice.scheduler;

import com.kerryprops.kip.bill.webservice.impl.DirectDebitsBillBatchController;
import com.querydsl.core.types.Predicate;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Pageable;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * AptBillSchedulerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @since - 2025-04-24
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("公寓小区-代扣批次检查失效-单元测试")
class AptBillSchedulerTest {

    @Mock
    private DirectDebitsBillBatchController batchController;

    @InjectMocks
    private AptBillScheduler aptBillScheduler;

    @Test
    @DisplayName("代扣批次检查失效，正常场景")
    void scheduleBBill_success() {
        // Act
        aptBillScheduler.scheduleBBill();

        // Assert
        verify(batchController, times(1)).lapsedCheck();
    }
}
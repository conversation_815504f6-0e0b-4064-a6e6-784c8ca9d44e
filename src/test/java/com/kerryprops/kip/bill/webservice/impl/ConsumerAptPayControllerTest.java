package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.service.AptBillPaymentCallbackService;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.webservice.vo.req.AptPaymentVo;
import com.kerryprops.kip.bill.webservice.vo.resp.BillSessionResult;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource;
import com.kerryprops.kip.pmw.client.resource.AsyncPaymentFailedResource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.doThrow;

@ExtendWith(MockitoExtension.class)
@DisplayName("C端合并支付和支付结果回调-单元测试")
class ConsumerAptPayControllerTest {

    @Mock
    private PaymentBillService paymentBillService;

    @Mock
    private AptBillPaymentCallbackService paymentCallbackService;

    @InjectMocks
    private ConsumerAptPayController consumerAptPayController;

    @Test
    @DisplayName("C端合并支付-单元测试")
    void _01_v2Pay_success() {
        RespWrapVo<BillSessionResult> respWrapVoActual = consumerAptPayController.v2Pay(List.of(new AptPaymentVo()));

        assertEquals(RespCodeEnum.SUCCESS.getCode(), respWrapVoActual.getCode());
        assertEquals(RespCodeEnum.SUCCESS.getMessage(), respWrapVoActual.getMessage());
    }

    @Test
    @DisplayName("C端支付-支付成功回调-回写异常场景-单元测试")
    void _02_callback_pay_success_api_error() {
        final String PAY_SUCCESS_EXCEPTION_MSG = "EXCEPTION_MSG";
        doThrow(new RuntimeException(PAY_SUCCESS_EXCEPTION_MSG)).when(paymentCallbackService).handlePaymentCallback(ArgumentMatchers.any());

        String respMessageActual = consumerAptPayController.callback(new AsynPaymentResultResource(null, null, null));

        assertEquals(PAY_SUCCESS_EXCEPTION_MSG, respMessageActual);
    }

    @Test
    @DisplayName("C端支付-支付成功回调-回写正常场景-单元测试")
    void _03_callback_pay_success_api_success() {
        String respMessageActual = consumerAptPayController.callback(new AsynPaymentResultResource(null, null, null));
        assertEquals("accepted", respMessageActual);
    }

    @Test
    @DisplayName("C端支付-支付失败回调-回写异常场景-单元测试")
    void _04_payFailedCallback_api_error() {
        final String PAY_FAILED_EXCEPTION_MSG = "EXCEPTION_MSG";
        doThrow(new RuntimeException(PAY_FAILED_EXCEPTION_MSG)).when(paymentCallbackService).handlePaymentFailedCallback(ArgumentMatchers.any());

        String respMessageActual = consumerAptPayController.payFailedCallback(new AsyncPaymentFailedResource(null, null, null));
        assertEquals(PAY_FAILED_EXCEPTION_MSG, respMessageActual);
    }

    @Test
    @DisplayName("C端支付-支付失败回调-回写正常场景-单元测试")
    void _05_payFailedCallback_api_success() {
        String respMessageActual = consumerAptPayController.payFailedCallback(new AsyncPaymentFailedResource(null, null, null));
        assertEquals("accepted", respMessageActual);
    }

    @Test
    @DisplayName("取消支付，正常场景")
    void cancelPayment_NormalScenario() {
        // Arrange
        String paymentId = "testPaymentId";

        // Act
        RespWrapVo result = consumerAptPayController.cancelPayment(paymentId);

        // Assert
        assertNotNull(result);
    }
}

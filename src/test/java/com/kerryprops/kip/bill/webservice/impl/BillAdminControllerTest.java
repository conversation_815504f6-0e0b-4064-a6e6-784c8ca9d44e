package com.kerryprops.kip.bill.webservice.impl;

import com.kerryprops.kip.bill.dao.AptErrJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.AptSyncJdeJobRepository;
import com.kerryprops.kip.bill.dao.AptSyncPaidBillToJdeRepository;
import com.kerryprops.kip.bill.dao.AptSyncPayToJdeRepository;
import com.kerryprops.kip.bill.dao.BillEmailTraceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSendRecordRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSyncRepository;
import com.kerryprops.kip.bill.dao.entity.QAptSyncPaidBillToJde;
import com.kerryprops.kip.bill.dao.entity.QAptSyncPayToJde;
import com.kerryprops.kip.bill.dao.entity.QEFapiaoSyncBill;
import com.kerryprops.kip.bill.webservice.scheduler.PaymentInvoiceScheduler;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


/**
 * BillAdminControllerTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-5-9
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("管理员Controller-单元测试")
class BillAdminControllerTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    @Qualifier("jdeJdbcTemplate")
    private JdbcTemplate jdeJdbcTemplate;

    @Mock
    private AptErrJdeBillRepository aptErrJdeBillRepository;

    @Mock
    private AptSyncJdeJobRepository aptSyncJdeJobRepository;

    @Mock
    private AptSyncPayToJdeRepository aptSyncPayToJdeRepository;

    @Mock
    private AptSyncPaidBillToJdeRepository aptSyncPaidBillToJdeRepository;

    @Mock
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @Mock
    private BillEmailTraceRepository emailTraceRepository;

    @Mock
    private EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Mock
    private EFapiaoSendRecordRepository eFapiaoSendRecordRepository;

    @Mock
    private EFapiaoSyncRepository eFapiaoSyncRepository;

    @Mock
    private PaymentInvoiceScheduler paymentInvoiceScheduler;

    @InjectMocks
    private BillAdminController billAdminController;

    @Test
    @DisplayName("jdbcInfo - 正常场景")
    void jdbcInfo_success() {
        // Arrange
        HikariDataSource mockDataSource = mock(HikariDataSource.class);
        HikariPoolMXBean mockPoolMXBean = mock(HikariPoolMXBean.class);
        when(jdeJdbcTemplate.getDataSource()).thenReturn(mockDataSource);
        when(jdbcTemplate.getDataSource()).thenReturn(mockDataSource);
        when(mockDataSource.getHikariPoolMXBean()).thenReturn(mockPoolMXBean);
        when(mockPoolMXBean.getIdleConnections()).thenReturn(5);
        when(mockPoolMXBean.getActiveConnections()).thenReturn(10);
        when(mockPoolMXBean.getTotalConnections()).thenReturn(15);
        when(mockPoolMXBean.getThreadsAwaitingConnection()).thenReturn(2);

        // Act
        Map<String, Object> result = (Map<String, Object>) billAdminController.jdbcInfo();

        // Assert
        assertEquals(2, result.size());
        verify(jdeJdbcTemplate, times(1)).getDataSource();
        verify(jdbcTemplate, times(1)).getDataSource();
    }

    @Test
    @DisplayName("aptSyncBillsError - 正常场景")
    void aptSyncBillsError_success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);

        // Act
        billAdminController.aptSyncBillsError(pageable);

        // Assert
        verify(aptErrJdeBillRepository, times(1)).findAll(pageable);
    }

    @Test
    @DisplayName("aptSyncJdeJob - 正常场景")
    void aptSyncJdeJob_success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);

        // Act
        billAdminController.aptSyncJdeJob(pageable);

        // Assert
        verify(aptSyncJdeJobRepository, times(1)).findAll(pageable);
    }

    @Test
    @DisplayName("syncPayToJde - 正常场景")
    void syncPayToJde_success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);

        // Act
        billAdminController.syncPayToJde(pageable, randomString());

        // Assert
        verify(aptSyncPayToJdeRepository, times(1)).findAll(any(BooleanExpression.class), any(Pageable.class));
    }

    @Test
    @DisplayName("paidBillToJde - 正常场景")
    void paidBillToJde_success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        String billNo = "BILL123";

        // Act
        billAdminController.paidBillToJde(pageable, billNo);

        // Assert
        verify(aptSyncPaidBillToJdeRepository, times(1)).findAll(any(BooleanExpression.class), any(Pageable.class));
    }

    @Test
    @DisplayName("paymentInfo - 正常场景")
    void paymentInfo_success() {
        // Arrange
        String id = "PAY123";

        // Act
        billAdminController.paymentInfo(id);

        // Assert
        verify(aptPaymentInfoRepository, times(1)).findById(id);
    }

    @Test
    @DisplayName("billInvoice - 正常场景")
    void billInvoice_getById_success() {
        // Arrange
        Long id = 1L;

        // Act
        billAdminController.billInvoice(id);

        // Assert
        verify(eFapiaoBillInvoiceRepository, times(1)).findById(id);
    }

    @Test
    @DisplayName("billInvoice - 正常场景：根据销售单号查询发票")
    void billInvoice_getBySalesBillNo_success() {
        // Arrange
        String salesBillNo = "BILL123";

        // Act
        billAdminController.billInvoice(salesBillNo);

        // Assert
        verify(eFapiaoBillInvoiceRepository, times(1)).findAllBySalesBillNo(salesBillNo);
    }

    @Test
    @DisplayName("billInvoiceSend - 正常场景：根据发票ID查询发送日志")
    void billInvoiceSend_success() {
        // Arrange
        Long billFapiaoId = 1L;

        // Act
        billAdminController.billInvoiceSend(billFapiaoId);

        // Assert
        verify(eFapiaoSendRecordRepository, times(1)).findAllByBillFapiaoIdIn(List.of(billFapiaoId));
    }

    @Test
    @DisplayName("billInvoiceSync - 正常场景：根据销售单号同步发票")
    void billInvoiceSync_success() {
        // Arrange
        String salesBillNo = "BILL123";

        QEFapiaoSyncBill qEFapiaoSyncBill = QEFapiaoSyncBill.eFapiaoSyncBill;
        Object mockResult = new Object();

        // Mock each level of the chain
        JPAQueryFactory mockJpaQueryFactory = mock(JPAQueryFactory.class);
        JPAQuery mockJPAQuery = mock(JPAQuery.class);

        when(eFapiaoSyncRepository.getJpaQueryFactory()).thenReturn(mockJpaQueryFactory);
        when(mockJpaQueryFactory.select(any(QEFapiaoSyncBill.class))).thenReturn(mockJPAQuery);
        when(mockJPAQuery.from(qEFapiaoSyncBill)).thenReturn(mockJPAQuery);
        when(mockJPAQuery.where(qEFapiaoSyncBill.salesBillNo.eq(salesBillNo))).thenReturn(mockJPAQuery);
        when(mockJPAQuery.fetch()).thenReturn(List.of(mockResult));

        // Act
        billAdminController.billInvoiceSync(salesBillNo);

        // Assert
        verify(mockJPAQuery, times(1)).fetch();
    }

    @Test
    @DisplayName("pullInvoicedStatus - 正常场景：拉取发票状态")
    void pullInvoicedStatus_success() {

        // Act
        billAdminController.pullInvoicedStatus();

        // Assert
        verify(paymentInvoiceScheduler, times(1)).pullInvoicedStatus();
    }

    @Test
    @DisplayName("同步支付到JDE，正常场景")
    void syncPayToJde2_success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Long docNo = 123L;
        when(aptSyncPayToJdeRepository.findAll(any(Predicate.class), any(Pageable.class))).thenReturn(Page.empty());

        // Act
        Object result = billAdminController.syncPayToJde2(pageable, docNo);

        // Assert
        assertNotNull(result);
        verify(aptSyncPayToJdeRepository, times(1)).findAll(eq(QAptSyncPayToJde.aptSyncPayToJde.rcdoc.eq(docNo)), eq(pageable));
    }

    @Test
    @DisplayName("同步已支付账单到JDE，正常场景")
    void paidBillToJde2_success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Long docNo = 456L;
        when(aptSyncPaidBillToJdeRepository.findAll(any(Predicate.class), any(Pageable.class)))
                .thenReturn(Page.empty());

        // Act
        Object result = billAdminController.paidBillToJde2(pageable, docNo);

        // Assert
        assertNotNull(result);
        verify(aptSyncPaidBillToJdeRepository, times(1)).findAll(eq(QAptSyncPaidBillToJde.aptSyncPaidBillToJde.rcdoc.eq(docNo)), eq(pageable));
    }

    @Test
    @DisplayName("查询邮件批次日志，正常场景")
    void billEmailTrace_success() {
        // Arrange
        String batchNo = "BATCH123";
        when(emailTraceRepository.findAllByBatchNo(batchNo)).thenReturn(Collections.emptyList());

        // Act
        Object result = billAdminController.billEmailTrace(batchNo);

        // Assert
        assertNotNull(result);
        verify(emailTraceRepository, times(1)).findAllByBatchNo(batchNo);
    }

}
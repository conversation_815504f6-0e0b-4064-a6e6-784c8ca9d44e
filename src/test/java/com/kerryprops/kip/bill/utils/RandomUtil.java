package com.kerryprops.kip.bill.utils;

import com.kerryprops.kip.bill.common.current.LoginUser;
import org.apache.commons.lang3.RandomStringUtils;
import org.instancio.Instancio;
import org.instancio.Select;
import org.springframework.data.annotation.Id;

import java.util.concurrent.ThreadLocalRandom;

/**
 * 随机工具类.
 *
 * <AUTHOR> 2024-03-30 15:23:05
 **/
public final class RandomUtil {

    private RandomUtil() {
    }

    public static <T> T randomObject(Class<T> clazz) {
        return Instancio.of(clazz)
                        .ignore(Select.fields()
                                      .annotated(Id.class))
                        .create();
    }

    /**
     * 生成固定长度的随机字符串
     */
    public static String randomString() {
        final int RANDOM_STRING_LENGTH = 10;
        return RandomStringUtils.randomAlphanumeric(RANDOM_STRING_LENGTH);
    }

    /**
     * 生成指定长度的随机字符串
     */
    public static String randomString(int length) {
        return RandomStringUtils.randomAlphanumeric(length);
    }

    /**
     * 生成随机的登录用户
     */
    public static LoginUser randomLoginUser() {
        return LoginUser.builder()
                        .nickName(randomString())
                        .fromType("C")
                        .userId(ThreadLocalRandom.current()
                                                 .nextLong())
                        .cid(randomString())
                        .projectIds(randomString())
                        .build();
    }

}

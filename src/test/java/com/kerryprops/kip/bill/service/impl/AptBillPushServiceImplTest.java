package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.BillNotifyChannel;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.config.properties.BillPushProperties;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.feign.clients.BUserClient;
import com.kerryprops.kip.bill.feign.clients.CUserClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.MessageDto;
import com.kerryprops.kip.bill.feign.entity.TenantStaffResponse;
import com.kerryprops.kip.bill.feign.entity.WxOpenIDResource;
import com.kerryprops.kip.bill.feign.entity.WxTemplateMsgRequestCommand;
import com.kerryprops.kip.bill.service.AptBillOperationService;
import com.kerryprops.kip.bill.service.model.s.BillReceiver;
import com.kerryprops.kip.bill.webservice.vo.req.BillNotifyRequest;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import org.instancio.Instancio;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.DIRECT_DEBIT_PAID;
import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.PAYING;
import static com.kerryprops.kip.bill.common.enums.BillPaymentStatus.TO_BE_PAID;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * AptBillPushServiceImplTest.
 *
 * <AUTHOR> Yu 2024-08-21 18:10:44
 **/
@ExtendWith(MockitoExtension.class)
@DisplayName("公寓小区-账单推送-单元测试")
class AptBillPushServiceImplTest {

    public static final List<BillNotifyChannel> ALL_BILL_NOTIFY_CHANNELS = Arrays.stream(BillNotifyChannel.values())
                                                                                 .toList();

    @Mock
    AptBillService aptBillService;

    @Mock
    BUserClient bUserClient;

    @Mock
    MessageClient messageClient;

    @Mock
    MessageCenterClient messageCenterClient;

    @Mock
    DataMigrationConfig dm;

    @Mock
    AptBillOperationService operationService;

    @Mock
    BillPushProperties billPushProperties;

    @Mock
    CUserClient profileClient;

    @Mock
    HiveAsClient hiveAsClient;

    @InjectMocks
    AptBillPushServiceImpl aptBillPushServiceImpl;

    @Test
    @DisplayName("pushAptBill-有效参数-正常推送")
    void pushAptBill_validRequest_success() {
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(TO_BE_PAID);
        when(aptBillService.getBillById(anyLong())).thenReturn(aptBill);
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(Instancio.create(RoomResp.class)));
        when(bUserClient.getStaffList(any(), anyInt(), anyString())).thenReturn(
                new RespWrapVo<>(List.of(Instancio.create(TenantStaffResponse.class))));
        when(messageClient.sendWxTemplateMessage(any())).thenReturn(new RespWrapVo<>(Boolean.TRUE));
        when(messageCenterClient.sendMessage(any())).thenReturn(new RespWrapVo<>("data"));
        when(dm.getWxMsgUrl()).thenReturn("getWxMsgUrlResponse");
        when(operationService.saveOperationLog(any(), anyString(), any(), any(), anyString(), anyString())).thenReturn(
                true);
        String roomId = aptBill.getRoomId();

        aptBillPushServiceImpl.pushAptBill(Map.of(roomId, List.of(aptBill)), Set.of(roomId), AptBillOperator.PUSH_ALL,
                                           aptBill.getProjectId());

        verify(aptBillService, times(1)).updateBillStatus4Push(aptBill.getId(), BillPushStatus.PUSHED,
                                                               BillStatus.TO_BE_PAID);
    }

    @Test
    @DisplayName("pushOneRoomBill-站内信和微信模板消息都发送失败-推送失败")
    void pushOneRoomBill_messageAndWechatMeassageAllSendFailed() {
        // Arrange
        mockPushInitialData();
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(PAYING);
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(Instancio.create(RoomResp.class)));
        when(aptBillService.getBillById(anyLong())).thenReturn(aptBill);

        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenReturn(
                new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR));
        when(messageClient.sendWxTemplateMessage(any(WxTemplateMsgRequestCommand.class))).thenReturn(
                new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR));

        // Act
        aptBillPushServiceImpl.pushOneRoomBill(aptBill.getRoomId(), List.of(aptBill), AptBillOperator.UNKNOWN);

        // Assert
        verify(aptBillService, times(1)).updateBillStatus4Push(anyLong(), eq(BillPushStatus.PUSH_FAILED), any());
    }

    @Test
    @DisplayName("pushOneRoomBill-站内信和微信模板消息都发送异常-推送失败")
    void pushOneRoomBill_messageAndWechatMeassageAllSendException() {
        // Arrange
        mockPushInitialData();
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(PAYING);
        when(aptBillService.getBillById(anyLong())).thenReturn(aptBill);
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(Instancio.create(RoomResp.class)));

        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenThrow(new RuntimeException("站内信发送失败"));
        when(messageClient.sendWxTemplateMessage(any(WxTemplateMsgRequestCommand.class))).thenThrow(
                new RuntimeException("微信模板消息发送失败"));

        // Act
        aptBillPushServiceImpl.pushOneRoomBill(aptBill.getRoomId(), List.of(aptBill), AptBillOperator.UNKNOWN);

        // Assert
        verify(aptBillService, times(1)).updateBillStatus4Push(eq(aptBill.getId()), eq(BillPushStatus.PUSH_FAILED),
                                                               any());
    }

    @Test
    @DisplayName("pushOneRoomBill-跳过已支付账单-正常结束")
    void pushOneRoomBill_skipPaidBill_success() {
        // Arrange
        mockPushInitialData();
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(DIRECT_DEBIT_PAID);
        when(aptBillService.getBillById(anyLong())).thenReturn(aptBill);
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(Instancio.create(RoomResp.class)));

        // Act
        aptBillPushServiceImpl.pushOneRoomBill(aptBill.getRoomId(), List.of(aptBill), AptBillOperator.UNKNOWN);

        // Assert
        verify(aptBillService, never()).updateBillStatus4Push(anyLong(), any(), any());
    }

    @Test
    @DisplayName("pushOneRoomBill-支付中的账单-正常推送")
    void pushOneRoomBill_payingBill_success() {
        // Arrange
        mockPushInitialData();
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(PAYING);
        when(aptBillService.getBillById(any())).thenReturn(aptBill);
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(Instancio.create(RoomResp.class)));

        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenReturn(new RespWrapVo<>());
        when(messageClient.sendWxTemplateMessage(any(WxTemplateMsgRequestCommand.class))).thenReturn(
                new RespWrapVo<>());

        // Act
        aptBillPushServiceImpl.pushOneRoomBill(aptBill.getRoomId(), List.of(aptBill), AptBillOperator.UNKNOWN);

        // Assert
        verify(aptBillService, times(1)).updateBillStatus4Push(aptBill.getId(), BillPushStatus.PUSHED,
                                                               BillStatus.TO_BE_PAID);
    }

    @Test
    @DisplayName("pushAptBill-查询房间信息时抛出异常-账单推送失败")
    void pushAptBill_fetchRoomThrowsException_pushFailed() {
        // 准备测试数据
        String roomId = "test-room-1";
        String projectId = "test-project";
        Map<String, List<AptBill>> roomBillMap = Map.of(roomId, List.of(createTestAptBill(roomId)));
        Set<String> roomIds = Set.of(roomId);
        AptBillOperator operator = AptBillOperator.PUSH_ALL;

        // Mock外部服务调用抛出异常
        when(hiveAsClient.getRoomById(roomId)).thenThrow(new RuntimeException("网络异常"));

        // 执行测试方法
        aptBillPushServiceImpl.pushAptBill(roomBillMap, roomIds, operator, projectId);

        // 验证行为和结果
        verify(hiveAsClient).getRoomById(roomId);
        verify(aptBillService).updateBillStatus4Push(anyLong(), eq(BillPushStatus.PUSH_FAILED), any());
    }

    @Test
    @DisplayName("pushAptBill-未找到房间信息-账单推送失败")
    void pushAptBill_roomNotFound_pushFailed() {
        // 准备测试数据
        String roomId = "test-room-1";
        String projectId = "test-project";
        Map<String, List<AptBill>> roomBillMap = Map.of(roomId, List.of(createTestAptBill(roomId)));
        Set<String> roomIds = Set.of(roomId);
        AptBillOperator operator = AptBillOperator.PUSH_ALL;

        // Mock外部服务返回无效响应
        RespWrapVo<RoomResp> invalidResponse = new RespWrapVo<>();
        invalidResponse.setCode("500000");
        invalidResponse.setMessage("房间不存在");
        when(hiveAsClient.getRoomById(roomId)).thenReturn(invalidResponse);

        // 执行测试方法
        aptBillPushServiceImpl.pushAptBill(roomBillMap, roomIds, operator, projectId);

        // 验证行为和结果
        verify(hiveAsClient).getRoomById(roomId);
        verify(aptBillService).updateBillStatus4Push(anyLong(), eq(BillPushStatus.PUSH_FAILED), any());
    }

    @Test
    @DisplayName("pushAptBill-房间配置信息不完整-账单推送失败")
    void pushAptBill_roomConfigInvalid_pushFailed() {
        // 准备测试数据
        String roomId = "test-room-1";
        String projectId = "test-project";
        Map<String, List<AptBill>> roomBillMap = Map.of(roomId, List.of(createTestAptBill(roomId)));
        Set<String> roomIds = Set.of(roomId);
        AptBillOperator operator = AptBillOperator.PUSH_ALL;

        // Mock外部服务返回无效房间配置
        RespWrapVo<RoomResp> response = new RespWrapVo<>();
        RoomResp roomResp = new RoomResp();
        // 设置room为null导致配置不完整
        roomResp.setRoom(null);
        response.setData(roomResp);
        when(hiveAsClient.getRoomById(roomId)).thenReturn(response);

        // 执行测试方法
        aptBillPushServiceImpl.pushAptBill(roomBillMap, roomIds, operator, projectId);

        // 验证行为和结果
        verify(hiveAsClient).getRoomById(roomId);
        verify(aptBillService).updateBillStatus4Push(anyLong(), eq(BillPushStatus.PUSH_FAILED), any());
    }

    @Test
    @DisplayName("pushAptBill-只有building为null-账单推送失败")
    void pushAptBill_buildingIsNull_pushFailed() {
        // 准备测试数据
        String roomId = "test-room-1";
        String projectId = "test-project";
        Map<String, List<AptBill>> roomBillMap = Map.of(roomId, List.of(createTestAptBill(roomId)));
        Set<String> roomIds = Set.of(roomId);
        AptBillOperator operator = AptBillOperator.PUSH_ALL;

        // Mock外部服务返回无效房间配置(只有building为null)
        RespWrapVo<RoomResp> response = new RespWrapVo<>();
        RoomResp roomResp = Instancio.create(RoomResp.class);
        roomResp.setBuilding(null); // building为null
        response.setData(roomResp);
        when(hiveAsClient.getRoomById(roomId)).thenReturn(response);

        // 执行测试方法
        aptBillPushServiceImpl.pushAptBill(roomBillMap, roomIds, operator, projectId);

        // 验证行为和结果
        verify(hiveAsClient).getRoomById(roomId);
        verify(aptBillService).updateBillStatus4Push(anyLong(), eq(BillPushStatus.PUSH_FAILED), any());
    }

    @Test
    @DisplayName("pushAptBill-未找到绑定的授权用户或业主-账单推送失败")
    void pushAptBill_notFoundUser_pushFailed() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        Map<String, List<AptBill>> billMap = Map.of("room1", List.of(aptBill));
        Set<String> roomIds = Set.of("room1");
        AptBillOperator aptBillOperator = AptBillOperator.ADD;
        String projectId = "project1";

        // Act and Assert
        aptBillPushServiceImpl.pushAptBill(billMap, roomIds, aptBillOperator, projectId);

        verify(aptBillService, times(1)).updateBillStatus4Push(eq(aptBill.getId()), eq(BillPushStatus.PUSH_FAILED),
                                                               any());
    }

    @Test
    @DisplayName("pushUnpaidBill-有效参数-正常推送")
    void pushUnpaidBill_validRequest_success() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        List<AptBill> aptBills = List.of(aptBill);
        when(dm.getWxMsgUrl()).thenReturn("getWxMsgUrlResponse");
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(Instancio.create(RoomResp.class)));
        when(bUserClient.getStaffList(any(), anyInt(), anyString())).thenReturn(
                new RespWrapVo<>(List.of(Instancio.create(TenantStaffResponse.class))));
        when(messageClient.sendWxTemplateMessage(any(WxTemplateMsgRequestCommand.class))).thenReturn(
                new RespWrapVo<>(Boolean.TRUE));
        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenReturn(new RespWrapVo<>("data"));

        // Act
        aptBillPushServiceImpl.pushUnpaidBill(ALL_BILL_NOTIFY_CHANNELS, aptBills, AptBillOperator.PUSH_ALL, "project1");

        // Assert
        verify(aptBillService, times(2)).updateBillStatus4Push(aptBill.getId(), BillPushStatus.PUSHED,
                                                               BillStatus.TO_BE_PAID);
    }

    @Test
    @DisplayName("pushUnpaidBill-未找到账单接收人-推送失败")
    void pushUnpaidBill_notFoundBillReceivers_failure() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setRoomId("room1");
        List<AptBill> aptBills = List.of(aptBill);
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(Instancio.create(RoomResp.class)));
        when(bUserClient.getStaffList(any(), anyInt(), anyString())).thenReturn(new RespWrapVo<>(List.of()));

        // Act
        aptBillPushServiceImpl.pushUnpaidBill(ALL_BILL_NOTIFY_CHANNELS, aptBills, AptBillOperator.PUSH_ALL, "project1");

        // Assert
        verify(aptBillService, times(1)).updateBillStatus4Push(aptBill.getId(), BillPushStatus.PUSH_FAILED, null);
    }

    @Test
    @DisplayName("pushUnpaidBill-所有消息发送失败-推送失败")
    void pushUnpaidBill_sendMessageFailures_pushFails() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setRoomId("room1");
        List<AptBill> aptBills = List.of(aptBill);
        mockPushInitialData();
        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenReturn(
                new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR));
        when(messageClient.sendWxTemplateMessage(any(WxTemplateMsgRequestCommand.class))).thenReturn(
                new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR));
        when(messageClient.sendSmsV2(any())).thenThrow(new RuntimeException("模拟网络错误"));
        when(messageClient.sendWxSubscribe(any())).thenThrow(new RuntimeException("网络错误"));
        when(profileClient.queryUserOpenIds(any(), any())).thenReturn(
                List.of(Instancio.create(WxOpenIDResource.class)));

        // Act
        aptBillPushServiceImpl.pushUnpaidBill(ALL_BILL_NOTIFY_CHANNELS, aptBills, AptBillOperator.PUSH_ALL, "project1");

        // Assert
        verify(aptBillService, times(3)).updateBillStatus4Push(eq(aptBill.getId()), eq(BillPushStatus.PUSH_FAILED),
                                                               any());
    }

    @Test
    @DisplayName("pushUnpaidBill-部分消息发送失败-部分成功")
    void pushUnpaidBill_partialMessageFailures_partialSuccess() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setRoomId("room1");
        List<AptBill> aptBills = List.of(aptBill);
        mockPushInitialData();
        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenReturn(new RespWrapVo<>("success"));
        when(messageClient.sendWxTemplateMessage(any(WxTemplateMsgRequestCommand.class))).thenReturn(
                new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR));
        when(profileClient.queryUserOpenIds(any(), any())).thenReturn(
                List.of(Instancio.create(WxOpenIDResource.class)));
        when(messageClient.sendWxSubscribe(any())).thenReturn(new RespWrapVo<>(true));

        // Act
        aptBillPushServiceImpl.pushUnpaidBill(ALL_BILL_NOTIFY_CHANNELS, aptBills, AptBillOperator.PUSH_ALL, "project1");

        // Assert
        verify(aptBillService, times(3)).updateBillStatus4Push(aptBill.getId(), BillPushStatus.PUSHED,
                                                               BillStatus.TO_BE_PAID);
    }

    @Test
    @DisplayName("pushUnpaidBillForSelectUsers-Valid parameters-Successful Push")
    void pushUnpaidBillForSelectUsers_validRequest_success() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
        List<AptBill> aptBills = List.of(aptBill);
        TenantStaffResponse staffResponse = Instancio.create(TenantStaffResponse.class);
        List<BillNotifyRequest.SelectUser> selectUsers =
                List.of(new BillNotifyRequest.SelectUser(staffResponse.getUserId(), aptBill.getRoomId()));

        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(Instancio.create(RoomResp.class)));
        when(bUserClient.getStaffList(any(), anyInt(), anyString())).thenReturn(
                new RespWrapVo<>(List.of(staffResponse)));

        // Act
        aptBillPushServiceImpl.pushUnpaidBillForSelectUsers(ALL_BILL_NOTIFY_CHANNELS, aptBills, selectUsers,
                                                            AptBillOperator.PUSH_SELECTED, "project1");

        // Assert
        verify(aptBillService, times(1)).updateBillStatus4Push(anyLong(), any(), any());
    }

    @Test
    @DisplayName("pushUnpaidBillForSelectUsers-No receivers found-Push Fails")
    void pushUnpaidBillForSelectUsers_noBillReceivers_pushFails() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setRoomId("room1");
        List<AptBill> aptBills = List.of(aptBill);
        List<BillNotifyRequest.SelectUser> selectUsers =
                List.of(new BillNotifyRequest.SelectUser("randomUser", aptBill.getRoomId()));
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(Instancio.create(RoomResp.class)));
        when(bUserClient.getStaffList(any(), anyInt(), anyString())).thenReturn(new RespWrapVo<>(List.of()));

        // Act
        aptBillPushServiceImpl.pushUnpaidBillForSelectUsers(ALL_BILL_NOTIFY_CHANNELS, aptBills, selectUsers,
                                                            AptBillOperator.PUSH_SELECTED, "project1");

        // Assert
        verify(aptBillService, times(1)).updateBillStatus4Push(aptBill.getId(), BillPushStatus.PUSH_FAILED, null);
    }

    @Test
    @DisplayName("pushUnpaidBillForSelectUsers-Partial message failures-Partial Success")
    void pushUnpaidBillForSelectUsers_partialMessageFailures_partialSuccess() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setRoomId("room1");
        List<AptBill> aptBills = List.of(aptBill);
        TenantStaffResponse staffResponse = Instancio.create(TenantStaffResponse.class);
        List<BillNotifyRequest.SelectUser> selectUsers =
                List.of(new BillNotifyRequest.SelectUser(staffResponse.getUserId(), aptBill.getRoomId()));
        RoomResp roomResp = Instancio.create(RoomResp.class);
        roomResp.getRoom()
                .setId(aptBill.getRoomId());
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(roomResp));
        when(bUserClient.getStaffList(any(), anyInt(), anyString())).thenReturn(
                new RespWrapVo<>(List.of(staffResponse)));
        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenReturn(new RespWrapVo<>("success"));
        when(messageClient.sendWxTemplateMessage(any(WxTemplateMsgRequestCommand.class))).thenReturn(
                new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR));

        // Act
        aptBillPushServiceImpl.pushUnpaidBillForSelectUsers(ALL_BILL_NOTIFY_CHANNELS, aptBills, selectUsers,
                                                            AptBillOperator.PUSH_SELECTED, "project1");

        // Assert
        verify(aptBillService, times(3)).updateBillStatus4Push(anyLong(), any(), any());
    }

    @Test
    @DisplayName("pushUnpaidBillForSelectUsers-All receivers fail-All Push Fails")
    void pushUnpaidBillForSelectUsers_allUsersFail_pushFails() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setRoomId("room1");
        List<AptBill> aptBills = List.of(aptBill);
        TenantStaffResponse staffResponse = Instancio.create(TenantStaffResponse.class);
        List<BillNotifyRequest.SelectUser> selectUsers =
                List.of(new BillNotifyRequest.SelectUser(staffResponse.getUserId(), aptBill.getRoomId()));
        RoomResp roomResp = Instancio.create(RoomResp.class);
        roomResp.getRoom()
                .setId(aptBill.getRoomId());
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(roomResp));
        when(bUserClient.getStaffList(any(), anyInt(), anyString())).thenReturn(
                new RespWrapVo<>(List.of(staffResponse)));

        when(messageCenterClient.sendMessage(any(MessageDto.class))).thenReturn(
                new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR));
        when(messageClient.sendWxTemplateMessage(any(WxTemplateMsgRequestCommand.class))).thenReturn(
                new RespWrapVo<>(RespCodeEnum.UNKNOWN_ERROR));
        when(messageClient.sendSmsV2(any())).thenThrow(new RuntimeException("模拟网络错误"));
        when(messageClient.sendWxSubscribe(any())).thenThrow(new RuntimeException("模拟网络错误"));
        when(profileClient.queryUserOpenIds(any(), any())).thenReturn(
                List.of(Instancio.create(WxOpenIDResource.class)));

        // Act
        aptBillPushServiceImpl.pushUnpaidBillForSelectUsers(ALL_BILL_NOTIFY_CHANNELS, aptBills, selectUsers,
                                                            AptBillOperator.PUSH_SELECTED, "project1");

        // Assert
        verify(aptBillService, times(3)).updateBillStatus4Push(eq(aptBill.getId()), eq(BillPushStatus.PUSH_FAILED),
                                                               any());
    }

    @Test
    @DisplayName("findBillReceivers-valid bills-Receivers Fetched Successfully")
    void findBillReceivers_validBills_success() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setRoomId("room1");
        List<AptBill> bills = List.of(aptBill);
        TenantStaffResponse staffResponse = Instancio.create(TenantStaffResponse.class);
        BillReceiver expectedReceiver = BillReceiver.of(aptBill, staffResponse);

        when(bUserClient.getStaffList(any(), eq(1), eq("room1"))).thenReturn(new RespWrapVo<>(List.of(staffResponse)));
        when(bUserClient.getStaffList(any(), eq(2), eq("room1"))).thenReturn(new RespWrapVo<>(List.of()));

        // Act
        Set<BillReceiver> actualReceivers = aptBillPushServiceImpl.findBillReceivers(bills);

        // Assert
        assertThat(actualReceivers).containsExactly(expectedReceiver);
    }

    @Test
    @DisplayName("findBillReceivers-Duplicate room IDs-No Duplicate Receivers")
    void findBillReceivers_duplicateRoomIds_noDuplicateReceivers() {
        // Arrange
        AptBill aptBill1 = randomObject(AptBill.class);
        aptBill1.setRoomId("room1");
        AptBill aptBill2 = randomObject(AptBill.class);
        aptBill2.setRoomId("room1");
        List<AptBill> bills = List.of(aptBill1, aptBill2);
        TenantStaffResponse staffResponse = Instancio.create(TenantStaffResponse.class);
        BillReceiver expectedReceiver = BillReceiver.of(aptBill1, staffResponse);

        when(bUserClient.getStaffList(any(), eq(1), eq("room1"))).thenReturn(new RespWrapVo<>(List.of(staffResponse)));
        when(bUserClient.getStaffList(any(), eq(2), eq("room1"))).thenReturn(new RespWrapVo<>(List.of()));

        // Act
        Set<BillReceiver> actualReceivers = aptBillPushServiceImpl.findBillReceivers(bills);

        // Assert
        assertThat(actualReceivers).containsExactly(expectedReceiver);
    }

    @Test
    @DisplayName("findBillReceivers-No Receivers Found-Empty Set Returned")
    void findBillReceivers_noReceivers_emptySet() {
        // Arrange
        AptBill aptBill = randomObject(AptBill.class);
        aptBill.setRoomId("room1");
        List<AptBill> bills = List.of(aptBill);

        when(bUserClient.getStaffList(any(), eq(1), eq("room1"))).thenReturn(new RespWrapVo<>(List.of()));
        when(bUserClient.getStaffList(any(), eq(2), eq("room1"))).thenReturn(new RespWrapVo<>(List.of()));

        // Act
        Set<BillReceiver> actualReceivers = aptBillPushServiceImpl.findBillReceivers(bills);

        // Assert
        assertThat(actualReceivers).isEmpty();
    }

    // 辅助方法，创建测试数据
    private AptBill createTestAptBill(String roomId) {
        AptBill aptBill = Instancio.create(AptBill.class);
        aptBill.setRoomId(roomId);
        return aptBill;
    }

    private void mockPushInitialData() {
        when(hiveAsClient.getRoomById(anyString())).thenReturn(new RespWrapVo<>(Instancio.create(RoomResp.class)));
        TenantStaffResponse staffResponse = Instancio.create(TenantStaffResponse.class);
        when(bUserClient.getStaffList(any(), anyInt(), anyString())).thenReturn(
                new RespWrapVo<>(List.of(staffResponse)));
    }

}
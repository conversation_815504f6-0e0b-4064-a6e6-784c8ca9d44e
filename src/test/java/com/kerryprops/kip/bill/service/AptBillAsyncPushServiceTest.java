package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.service.impl.AptBillAsyncPushServiceImpl;
import org.instancio.Instancio;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;

/**
 * AptBillAsyncPushServiceTest.
 *
 * <AUTHOR> Yu 2024-08-09 09:52:28
 **/
@ExtendWith(MockitoExtension.class)
class AptBillAsyncPushServiceTest {

    @InjectMocks
    AptBillAsyncPushServiceImpl aptBillAsyncPushService;

    @Mock
    AptBillPushService aptBillPushService;

    @Test
    @DisplayName("asyncPushAptBill-正常入参-正常推送")
    void asyncPushAptBill_validRequest_success() {
        aptBillAsyncPushService.asyncPushAptBill(List.of(Instancio.create(AptBill.class)), AptBillOperator.PUSH_ALL,
                                                 "cid", Instancio.create(LoginUser.class));

        verify(aptBillPushService).pushAptBill(anyMap(), anySet(), any(), anyString());
    }

}
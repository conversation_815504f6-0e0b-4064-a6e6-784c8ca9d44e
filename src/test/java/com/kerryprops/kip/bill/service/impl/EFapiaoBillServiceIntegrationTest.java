package com.kerryprops.kip.bill.service.impl;

import com.github.dozermapper.core.Mapper;
import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.enums.InvoiceIssueStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceRedflagStatusEnum;
import com.kerryprops.kip.bill.common.enums.InvoiceState;
import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.InvoiceUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSyncRepository;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.dao.entity.EFapiaoSyncBill;
import com.kerryprops.kip.bill.dao.entity.QEFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.QEFapiaoSyncBill;
import com.kerryprops.kip.bill.service.impl.EFapiaoBillServiceImpl;
import com.kerryprops.kip.bill.service.impl.EFapiaoJdeBillServiceImpl;
import com.kerryprops.kip.bill.utils.InvoiceTestUtils;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceDetailVo;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.C;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.CE;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.QC;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.S;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.SE;
import static com.kerryprops.kip.bill.common.utils.InvoiceUtils.splitJdeSalesbillNo;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;

@DisplayName("JDE同步-提交开票请求/发票信息回写Kerry+")
class EFapiaoBillServiceIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Autowired
    private EFapiaoSyncRepository eFapiaoSyncRepository;

    @Autowired
    private EFapiaoBillServiceImpl eFapiaoBillService;

    @Autowired
    private EFapiaoJdeBillServiceImpl eFapiaoJdeBillService;

    @Autowired
    private Mapper mapperAutowired;

    @Autowired
    private BeanUtil beanUtil;

    @Mock
    private Mapper mapperMock;

    @Test
    @DisplayName("提交开票请求：异常场景，入参为null")
    void _01_invoiceUpload_param_is_null_exception() {
        this.clearData();
        assertThrows(RuntimeException.class, () -> eFapiaoBillService.invoiceUpload(null));
    }

    @Test
    @DisplayName("提交开票请求：异常场景，开票请求响应异常")
    void _02_invoiceUpload_response_fail() {
        RespWrapVo respWrapVo = new RespWrapVo();
        respWrapVo.setCode(RespCodeEnum.NOT_FOUND.getCode());
        doReturn(respWrapVo).when(kipInvoiceClient).invoiceUpload(ArgumentMatchers.any());

        assertThrows(RuntimeException.class, () -> eFapiaoBillService.invoiceUpload(getRandomEFapiaoJDEBill()));
    }

    @Test
    @DisplayName("提交开票请求：异常场景-报指定异常后回滚")
    void _03_invoiceUpload_null_pointer_exception_error() {
        RespWrapVo respWrapVo = new RespWrapVo();
        respWrapVo.setCode(RespCodeEnum.SUCCESS.getCode());
        doReturn(respWrapVo).when(kipInvoiceClient).invoiceUpload(ArgumentMatchers.any());

        doNothing().when(eFapiaoJdeBillService).writeBackUploaded(ArgumentMatchers.any());

        EFapiaoJDEBill eFapiaoJDEBill = getRandomEFapiaoJDEBill();
        eFapiaoJDEBill.setInvoiceType("N");

        ReflectionTestUtils.setField(beanUtil, "mapperCache", mapperMock);

        // 执行待测代码
        assertThrows(NullPointerException.class, () -> eFapiaoBillService.invoiceUpload(eFapiaoJDEBill));

        String salesbillNo = eFapiaoJDEBill.getKco()
                + eFapiaoJDEBill.getBillType()
                + eFapiaoJDEBill.getDoc()
                + eFapiaoJDEBill.getPaymentItem();
        EFapiaoSyncBill eFapiaoSyncBillActual = eFapiaoSyncRepository.findOne(QEFapiaoSyncBill.eFapiaoSyncBill
                .salesBillNo.eq(salesbillNo)).orElse(null);
        assertNull(eFapiaoSyncBillActual);
    }

    @Test
    @DisplayName("提交开票请求：正常场景")
    void _04_invoiceUpload_response_success() {
        RespWrapVo respWrapVo = new RespWrapVo();
        respWrapVo.setCode(RespCodeEnum.SUCCESS.getCode());
        doReturn(respWrapVo).when(kipInvoiceClient).invoiceUpload(ArgumentMatchers.any());

        doNothing().when(eFapiaoJdeBillService).writeBackUploaded(ArgumentMatchers.any());

        EFapiaoJDEBill eFapiaoJDEBill = getRandomEFapiaoJDEBill();
        eFapiaoJDEBill.setInvoiceType("N");
        eFapiaoJDEBill.setDoco(randomString(200));

        ReflectionTestUtils.setField(beanUtil, "mapperCache", mapperAutowired);

        // 执行待测代码
        eFapiaoBillService.invoiceUpload(eFapiaoJDEBill);

        // 功能验证
        String no = eFapiaoJDEBill.getKco()
                + eFapiaoJDEBill.getBillType()
                + eFapiaoJDEBill.getDoc()
                + eFapiaoJDEBill.getPaymentItem();
        EFapiaoSyncBill eFapiaoSyncBill = eFapiaoSyncRepository.findOne(QEFapiaoSyncBill.eFapiaoSyncBill
                .salesBillNo.eq(no)).orElseThrow();
        assertEquals(eFapiaoJDEBill.getKco(), eFapiaoSyncBill.getKco());
        assertEquals(eFapiaoJDEBill.getBillType(), eFapiaoSyncBill.getBillType());
        assertEquals(eFapiaoJDEBill.getDoc(), eFapiaoSyncBill.getDoc());
        assertEquals(eFapiaoJDEBill.getPaymentItem(), eFapiaoSyncBill.getPaymentItem());
        assertEquals(eFapiaoJDEBill.getCompanyCode(), eFapiaoSyncBill.getCompanyCode());
        assertEquals(eFapiaoJDEBill.getAn8(), eFapiaoSyncBill.getAn8());
        assertEquals(eFapiaoJDEBill.getPurchaserName(), eFapiaoSyncBill.getPurchaserName());
        assertEquals(eFapiaoJDEBill.getPurchaserTaxNo(), eFapiaoSyncBill.getPurchaserTaxNo());
        assertEquals(eFapiaoJDEBill.getPurchaserAddress(), eFapiaoSyncBill.getPurchaserAddress());
        assertEquals(eFapiaoJDEBill.getPurchaserBankName(), eFapiaoSyncBill.getPurchaserBankName());
        assertEquals(eFapiaoJDEBill.getMailingAddress(), eFapiaoSyncBill.getMailingAddress());
        assertEquals(eFapiaoJDEBill.getMcu(), eFapiaoSyncBill.getMcu());
        assertEquals(eFapiaoJDEBill.getDoco(), eFapiaoSyncBill.getDoco());
        assertEquals(eFapiaoJDEBill.getBillCode(), eFapiaoSyncBill.getBillCode());
        assertEquals(eFapiaoJDEBill.getGoodsTaxNo(), eFapiaoSyncBill.getGoodsTaxNo());
        assertEquals(eFapiaoJDEBill.getItemName(), eFapiaoSyncBill.getItemName());
        assertEquals(eFapiaoJDEBill.getItemSpec(), eFapiaoSyncBill.getItemSpec());
        assertEquals(eFapiaoJDEBill.getRealEstateNo(), eFapiaoSyncBill.getRealEstateNo());
        assertEquals(eFapiaoJDEBill.getRealEstatePlace(), eFapiaoSyncBill.getRealEstatePlace());
        assertEquals(0, eFapiaoJDEBill.getAmountWithoutTax().compareTo(eFapiaoSyncBill.getAmountWithoutTax()));
        assertEquals(0, eFapiaoJDEBill.getTaxRate().compareTo(eFapiaoSyncBill.getTaxRate()));
        assertEquals(0, eFapiaoJDEBill.getTaxAmount().compareTo(eFapiaoSyncBill.getTaxAmount()));
        assertEquals(0, eFapiaoJDEBill.getAmountWithTax().compareTo(eFapiaoSyncBill.getAmountWithTax()));
        assertEquals(eFapiaoJDEBill.getTaxDiscount(), eFapiaoSyncBill.getTaxDiscount());
        assertEquals(eFapiaoJDEBill.getTaxDiscountDesc(), eFapiaoSyncBill.getTaxDiscountDesc());
        assertEquals(eFapiaoJDEBill.getJdeUnit(), eFapiaoSyncBill.getJdeUnit());
        assertEquals(eFapiaoJDEBill.getTaxClassifyDesc(), eFapiaoSyncBill.getTaxClassifyDesc());
        assertEquals(C.getKipCode(), eFapiaoSyncBill.getInvoiceType());
        assertEquals(no, eFapiaoSyncBill.getSalesBillNo());
        assertEquals(InvoiceIssueStatus.UPLOAD_SUCCESS, eFapiaoSyncBill.getInvoiceIssueStatus());
    }

    @Test
    @DisplayName("开票信息回调：正常场景：开票状态失败")
    void _05_invoiceWriteBack_invoice_record_status_fail() {
        EFapiaoJDEBill eFapiaoJdeBill = getRandomEFapiaoJDEBill();
        eFapiaoJdeBill.setInvoiceType("NE");

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceType(SE.getCode());
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_FAILED);
        mainVo.setMessage(getRandomString());
        mainVo.setBusinessType(StringUtils.EMPTY);
        mainVo.setExt1(StringUtils.EMPTY);
        mainVo.setExt2(StringUtils.EMPTY);
        mainVo.setExt3(StringUtils.EMPTY);

        EFapiaoSyncBill eFapiaoSyncBill = BeanUtil.copy(eFapiaoJdeBill, EFapiaoSyncBill.class);
        eFapiaoSyncBill.setSalesBillNo(mainVo.getSalesbillNo());
        eFapiaoSyncBill.setInvoiceIssueStatus(InvoiceIssueStatus.UPLOAD_SUCCESS);
        eFapiaoSyncRepository.save(eFapiaoSyncBill);

        // 执行待测代码
        eFapiaoBillService.invoiceWriteBack(mainVo);

        // 功能验证
        List<EFapiaoBillInvoice> eFapiaoBillInvoices = eFapiaoBillInvoiceRepository.findAll();
        Optional<EFapiaoBillInvoice> eFapiaoBillInvoiceOptional = eFapiaoBillInvoices.stream()
                .max(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime));
        assertTrue(eFapiaoBillInvoiceOptional.isEmpty());

        List<EFapiaoSyncBill> eFapiaoSyncBills = eFapiaoSyncRepository.findAll();
        eFapiaoSyncBills.forEach(e -> {
            if (mainVo.getSalesbillNo().equals(e.getSalesBillNo())) {
                assertEquals(InvoiceIssueStatus.INVOICE_FAILED, e.getInvoiceIssueStatus());
            }
        });
    }

    @Test
    @DisplayName("开票信息回调：正常场景")
    void _06_invoiceWriteBack_invoice_state_normal_success() {
        EFapiaoJDEBill eFapiaoJdeBill = getRandomEFapiaoJDEBill();
        eFapiaoJdeBill.setInvoiceType("NE");

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setTaxRate("0.05,0.06");
        mainVo.setInvoiceType(CE.getCode());
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr());

        EFapiaoSyncBill eFapiaoSyncBill = BeanUtil.copy(eFapiaoJdeBill, EFapiaoSyncBill.class);
        eFapiaoSyncBill.setSalesBillNo(mainVo.getSalesbillNo());
        eFapiaoSyncBill.setInvoiceIssueStatus(InvoiceIssueStatus.UPLOAD_SUCCESS);
        eFapiaoSyncRepository.save(eFapiaoSyncBill);

        // 执行待测代码
        eFapiaoBillService.invoiceWriteBack(mainVo);

        // 功能验证
        List<EFapiaoBillInvoice> eFapiaoBillInvoices = eFapiaoBillInvoiceRepository.findAll();
        Optional<EFapiaoBillInvoice> eFapiaoBillInvoiceOptional
                = eFapiaoBillInvoices.stream()
                .max(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime));
        EFapiaoBillInvoice eFapiaoBillInvoice = eFapiaoBillInvoiceOptional.get();

        assertEquals("JDE", eFapiaoBillInvoice.getBizType());
        assertEquals(CE.getKipCode(), eFapiaoBillInvoice.getInvoiceType());
        assertEquals(InvoiceRecordStatus.COMPLETED, eFapiaoBillInvoice.getInvoiceRecordStatus());
        assertEquals(InvoiceState.NORMAL, eFapiaoBillInvoice.getState());

        checkInvoiceInfo(mainVo, eFapiaoBillInvoice);
        checkJdeBillInfo(mainVo, eFapiaoBillInvoice);

        mainVo.getDetailVos().forEach(detailVo -> {
            assertTrue(StringUtils.isEmpty(eFapiaoBillInvoice.getLeaseTermStart()));
            assertTrue(StringUtils.isEmpty(eFapiaoBillInvoice.getLeaseTermEnd()));
        });

        List<EFapiaoSyncBill> eFapiaoSyncBills = eFapiaoSyncRepository.findAll();
        eFapiaoSyncBills.forEach(e -> {
            if (mainVo.getSalesbillNo().equals(e.getSalesBillNo())) {
                assertEquals(InvoiceIssueStatus.INVOICE_SUCCESS, e.getInvoiceIssueStatus());
            }
        });
    }

    @Test
    @DisplayName("开票信息回调：正常场景，红冲发票第一次回调")
    void _07_invoiceWriteBack_invoice_state_red_status_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_RED_STATUS.getIndexStr());

        EFapiaoJDEBill eFapiaoJdeBill = getRandomEFapiaoJDEBill();
        EFapiaoBillInvoice eFapiaoBillInvoice = BeanUtil.copy(eFapiaoJdeBill, EFapiaoBillInvoice.class);
        eFapiaoBillInvoice.setSalesBillNo(mainVo.getSalesbillNo());
        eFapiaoBillInvoice.setSellerName(getRandomString());
        eFapiaoBillInvoice.setAmountWithoutTax(BigDecimal.valueOf(random.nextInt()));
        eFapiaoBillInvoice.setAmountWithTax(BigDecimal.valueOf(random.nextInt()));
        eFapiaoBillInvoice.setTaxAmount(BigDecimal.valueOf(random.nextInt()));
        eFapiaoBillInvoice.setPdfUrl(getRandomString());
        eFapiaoBillInvoice.setInvoiceType(S.getKipCode());
        eFapiaoBillInvoice.setInvoiceNo(mainVo.getInvoiceNo());
        eFapiaoBillInvoice.setInvoiceCode(getRandomString());
        eFapiaoBillInvoice.setPaperDrewDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        eFapiaoBillInvoice.setBizType("JDE");
        eFapiaoBillInvoice.setInvoiceRecordStatus(InvoiceRecordStatus.COMPLETED);
        eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice);

        EFapiaoSyncBill eFapiaoSyncBill = BeanUtil.copy(eFapiaoJdeBill, EFapiaoSyncBill.class);
        eFapiaoSyncBill.setSalesBillNo(mainVo.getSalesbillNo());
        eFapiaoSyncBill.setInvoiceIssueStatus(InvoiceIssueStatus.INVOICE_SUCCESS);
        eFapiaoSyncRepository.save(eFapiaoSyncBill);

        // 执行待测代码
        eFapiaoBillService.invoiceWriteBack(mainVo);

        // 功能验证
        List<EFapiaoBillInvoice> eFapiaoBillInvoices = eFapiaoBillInvoiceRepository.findAll();
        Optional<EFapiaoBillInvoice> eFapiaoBillInvoiceOptional = eFapiaoBillInvoices.stream()
                .max(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime));
        EFapiaoBillInvoice eFapiaoBillInvoiceCheck = eFapiaoBillInvoiceOptional.get();

        assertEquals(S.getKipCode(), eFapiaoBillInvoiceCheck.getInvoiceType());
        assertEquals(InvoiceRecordStatus.COMPLETED, eFapiaoBillInvoiceCheck.getInvoiceRecordStatus());
        assertEquals(InvoiceState.RED_STATUS, eFapiaoBillInvoiceCheck.getState());

        List<EFapiaoSyncBill> eFapiaoSyncBills = eFapiaoSyncRepository.findAll();
        eFapiaoSyncBills.forEach(e -> {
            if (mainVo.getSalesbillNo().equals(e.getSalesBillNo())) {
                assertEquals(InvoiceIssueStatus.INVOICE_SUCCESS_RED_STATUS, e.getInvoiceIssueStatus());
            }
        });
    }

    @Test
    @DisplayName("开票信息回调：正常场景，红冲发票第二次回调")
    void _08_invoiceWriteBack_invoice_state_red_flag_success() {
        EFapiaoJDEBill eFapiaoJdeBill = getRandomEFapiaoJDEBill();
        eFapiaoJdeBill.setInvoiceType("NE");

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceType(QC.getCode());
        mainVo.setAmountWithoutTax(BigDecimal.valueOf(-1 - random.nextInt(100000)));
        mainVo.setAmountWithTax(BigDecimal.valueOf(-1 - random.nextInt(100000)));
        mainVo.setTaxAmount(BigDecimal.valueOf(-1 - random.nextInt(100000)));
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_RED_INVOICE.getIndexStr());

        EFapiaoSyncBill eFapiaoSyncBill = BeanUtil.copy(eFapiaoJdeBill, EFapiaoSyncBill.class);
        eFapiaoSyncBill.setSalesBillNo(mainVo.getSalesbillNo());
        eFapiaoSyncBill.setInvoiceIssueStatus(InvoiceIssueStatus.UPLOAD_SUCCESS);
        eFapiaoSyncRepository.save(eFapiaoSyncBill);

        // 执行待测代码
        eFapiaoBillService.invoiceWriteBack(mainVo);

        // 功能验证
        List<EFapiaoBillInvoice> eFapiaoBillInvoices = eFapiaoBillInvoiceRepository.findAll();
        Optional<EFapiaoBillInvoice> eFapiaoBillInvoiceOptional = eFapiaoBillInvoices.stream()
                .max(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime));
        EFapiaoBillInvoice eFapiaoBillInvoice = eFapiaoBillInvoiceOptional.get();

        assertEquals(QC.getKipCode(), eFapiaoBillInvoice.getInvoiceType());
        assertEquals(InvoiceRecordStatus.COMPLETED, eFapiaoBillInvoice.getInvoiceRecordStatus());
        assertEquals(InvoiceState.RED_LETTER, eFapiaoBillInvoice.getState());

        checkInvoiceInfo(mainVo, eFapiaoBillInvoice);
        checkJdeBillInfo(mainVo, eFapiaoBillInvoice);

        mainVo.getDetailVos().forEach(detailVo -> {
            assertEquals(detailVo.getLeaseTermStart(), eFapiaoBillInvoice.getLeaseTermStart());
            assertEquals(detailVo.getLeaseTermEnd(), eFapiaoBillInvoice.getLeaseTermEnd());
        });

        List<EFapiaoSyncBill> eFapiaoSyncBills = eFapiaoSyncRepository.findAll();
        eFapiaoSyncBills.forEach(e -> {
            if (mainVo.getSalesbillNo().equals(e.getSalesBillNo())) {
                assertEquals(InvoiceIssueStatus.INVOICE_SUCCESS_RED_FLAG, e.getInvoiceIssueStatus());
            }
        });
    }

    @Test
    @DisplayName("开票信息回调：作废场景")
    void _09_invoiceWriteBack_invoice_status_cancel_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_CANCEL);

        EFapiaoJDEBill eFapiaoJdeBill = getRandomEFapiaoJDEBill();
        EFapiaoBillInvoice eFapiaoBillInvoice = BeanUtil.copy(eFapiaoJdeBill, EFapiaoBillInvoice.class);
        eFapiaoBillInvoice.setSalesBillNo(mainVo.getSalesbillNo());
        eFapiaoBillInvoice.setSellerName(getRandomString());
        eFapiaoBillInvoice.setAmountWithoutTax(BigDecimal.valueOf(random.nextInt()));
        eFapiaoBillInvoice.setAmountWithTax(BigDecimal.valueOf(random.nextInt()));
        eFapiaoBillInvoice.setTaxAmount(BigDecimal.valueOf(random.nextInt()));
        eFapiaoBillInvoice.setPdfUrl(getRandomString());
        eFapiaoBillInvoice.setXmlUrl(getRandomString());
        eFapiaoBillInvoice.setOfdUrl(getRandomString());
        eFapiaoBillInvoice.setInvoiceType(S.getKipCode());
        eFapiaoBillInvoice.setInvoiceNo(mainVo.getInvoiceNo());
        eFapiaoBillInvoice.setInvoiceCode(getRandomString());
        eFapiaoBillInvoice.setPaperDrewDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        eFapiaoBillInvoice.setMcu(getRandomString());
        eFapiaoBillInvoice.setBizType("JDE");
        eFapiaoBillInvoice.setInvoiceRecordStatus(InvoiceRecordStatus.COMPLETED);
        eFapiaoBillInvoiceRepository.save(eFapiaoBillInvoice);

        EFapiaoSyncBill eFapiaoSyncBill = BeanUtil.copy(eFapiaoJdeBill, EFapiaoSyncBill.class);
        eFapiaoSyncBill.setSalesBillNo(mainVo.getSalesbillNo());
        eFapiaoSyncBill.setInvoiceIssueStatus(InvoiceIssueStatus.UPLOAD_SUCCESS);
        eFapiaoSyncRepository.save(eFapiaoSyncBill);

        // 执行待测代码
        eFapiaoBillService.invoiceWriteBack(mainVo);

        // 功能验证
        List<EFapiaoBillInvoice> eFapiaoBillInvoices = eFapiaoBillInvoiceRepository.findAll();
        Optional<EFapiaoBillInvoice> eFapiaoBillInvoiceOptional = eFapiaoBillInvoices.stream()
                .max(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime));
        EFapiaoBillInvoice eFapiaoBillInvoiceCheck = eFapiaoBillInvoiceOptional.get();
        assertEquals(eFapiaoBillInvoiceCheck.getKco(), eFapiaoBillInvoice.getKco());
        assertEquals(eFapiaoBillInvoiceCheck.getCompanyCode(), eFapiaoBillInvoice.getCompanyCode());
        assertEquals(eFapiaoBillInvoiceCheck.getDoc(), eFapiaoBillInvoice.getDoc());
        assertEquals(eFapiaoBillInvoiceCheck.getPaymentItem(), eFapiaoBillInvoice.getPaymentItem());
        assertEquals(eFapiaoBillInvoiceCheck.getAn8(), eFapiaoBillInvoice.getAn8());
        assertEquals(eFapiaoBillInvoiceCheck.getPurchaserName(), eFapiaoBillInvoice.getPurchaserName());
        assertEquals(eFapiaoBillInvoiceCheck.getPurchaserTaxNo(), eFapiaoBillInvoice.getPurchaserTaxNo());
        assertEquals(eFapiaoBillInvoiceCheck.getPurchaserAddress(), eFapiaoBillInvoice.getPurchaserAddress());
        assertEquals(eFapiaoBillInvoiceCheck.getPurchaserBankName(), eFapiaoBillInvoice.getPurchaserBankName());
        assertEquals(eFapiaoBillInvoiceCheck.getInvoiceType(), eFapiaoBillInvoice.getInvoiceType());
        assertEquals(eFapiaoBillInvoiceCheck.getMailingAddress(), eFapiaoBillInvoice.getMailingAddress());
        assertEquals(eFapiaoBillInvoiceCheck.getJdeUnit(), eFapiaoBillInvoice.getJdeUnit());
        assertEquals(eFapiaoBillInvoiceCheck.getBillType(), eFapiaoBillInvoice.getBillType());
        assertEquals(eFapiaoBillInvoiceCheck.getGoodsTaxNo(), eFapiaoBillInvoice.getGoodsTaxNo());
        assertEquals(eFapiaoBillInvoiceCheck.getItemName(), eFapiaoBillInvoice.getItemName());
        assertEquals(eFapiaoBillInvoiceCheck.getItemSpec(), eFapiaoBillInvoice.getItemSpec());
        assertEquals(0, eFapiaoBillInvoiceCheck.getTaxRate().compareTo(eFapiaoBillInvoice.getTaxRate()));
        assertEquals(eFapiaoBillInvoiceCheck.getTaxDiscount(), eFapiaoBillInvoice.getTaxDiscount());
        assertEquals(eFapiaoBillInvoiceCheck.getTaxDiscountDesc(), eFapiaoBillInvoice.getTaxDiscountDesc());
        assertEquals(eFapiaoBillInvoiceCheck.getExtendedDoc(), eFapiaoBillInvoice.getExtendedDoc());
        assertEquals(eFapiaoBillInvoiceCheck.getBillDrawDate(), eFapiaoBillInvoice.getBillDrawDate());
        assertEquals(eFapiaoBillInvoiceCheck.getSecondName(), eFapiaoBillInvoice.getSecondName());
        assertEquals(eFapiaoBillInvoiceCheck.getFormatedDoco(), eFapiaoBillInvoice.getFormatedDoco());
        assertEquals(eFapiaoBillInvoiceCheck.getIsContactPerson(), eFapiaoBillInvoice.getIsContactPerson());
        assertEquals(eFapiaoBillInvoiceCheck.getIsTaxpayer(), eFapiaoBillInvoice.getIsTaxpayer());
        assertEquals(eFapiaoBillInvoiceCheck.getTaxClassifyDesc(), eFapiaoBillInvoice.getTaxClassifyDesc());
        assertEquals(eFapiaoBillInvoiceCheck.getUserName(), eFapiaoBillInvoice.getUserName());
        assertEquals(eFapiaoBillInvoiceCheck.getProgramId(), eFapiaoBillInvoice.getProgramId());
        assertEquals(eFapiaoBillInvoiceCheck.getCreateDateJde(), eFapiaoBillInvoice.getCreateDateJde());
        assertEquals(eFapiaoBillInvoiceCheck.getCreateTimeJde(), eFapiaoBillInvoice.getCreateTimeJde());
        assertEquals(eFapiaoBillInvoiceCheck.getProgramId(), eFapiaoBillInvoice.getProgramId());
        assertEquals(eFapiaoBillInvoiceCheck.getEvent1(), eFapiaoBillInvoice.getEvent1());
        assertEquals(eFapiaoBillInvoiceCheck.getEvent2(), eFapiaoBillInvoice.getEvent2());
        assertEquals(eFapiaoBillInvoiceCheck.getEvent3(), eFapiaoBillInvoice.getEvent3());
        assertEquals(eFapiaoBillInvoiceCheck.getDscrp1(), eFapiaoBillInvoice.getDscrp1());
        assertEquals(eFapiaoBillInvoiceCheck.getDscrp2(), eFapiaoBillInvoice.getDscrp2());
        assertEquals(eFapiaoBillInvoiceCheck.getDscrp3(), eFapiaoBillInvoice.getDscrp3());
        assertEquals(eFapiaoBillInvoiceCheck.getConstant1(), eFapiaoBillInvoice.getConstant1());
        assertEquals(eFapiaoBillInvoiceCheck.getConstant2(), eFapiaoBillInvoice.getConstant2());
        assertEquals(0, eFapiaoBillInvoiceCheck.getConstant3().compareTo(eFapiaoBillInvoice.getConstant3()));
        assertEquals(0, eFapiaoBillInvoiceCheck.getConstant4().compareTo(eFapiaoBillInvoice.getConstant4()));
        assertEquals(0, eFapiaoBillInvoiceCheck.getConstant5().compareTo(eFapiaoBillInvoice.getConstant5()));
        assertEquals(eFapiaoBillInvoiceCheck.getConstant6(), eFapiaoBillInvoice.getConstant6());
        assertEquals("JDE", eFapiaoBillInvoiceCheck.getBizType());
        assertEquals(InvoiceRecordStatus.COMPLETED, eFapiaoBillInvoiceCheck.getInvoiceRecordStatus());
        assertEquals(InvoiceState.CANCELLED, eFapiaoBillInvoiceCheck.getState());

        assertEquals(eFapiaoBillInvoiceCheck.getSalesBillNo(), eFapiaoBillInvoice.getSalesBillNo());
        assertEquals(eFapiaoBillInvoiceCheck.getSellerName(), eFapiaoBillInvoice.getSellerName());
        assertEquals(eFapiaoBillInvoiceCheck.getPdfUrl(), eFapiaoBillInvoice.getPdfUrl());
        assertEquals(eFapiaoBillInvoiceCheck.getXmlUrl(), eFapiaoBillInvoice.getXmlUrl());
        assertEquals(eFapiaoBillInvoiceCheck.getOfdUrl(), eFapiaoBillInvoice.getOfdUrl());
        assertEquals(eFapiaoBillInvoiceCheck.getMakingReason(), eFapiaoBillInvoice.getMakingReason());
        assertEquals(eFapiaoBillInvoiceCheck.getMcu(), eFapiaoBillInvoice.getMcu());
        assertEquals(0, eFapiaoBillInvoiceCheck.getAmountWithoutTax().compareTo(eFapiaoBillInvoice.getAmountWithoutTax()));
        assertEquals(0, eFapiaoBillInvoiceCheck.getTaxAmount().compareTo((eFapiaoBillInvoice.getTaxAmount())));
        assertEquals(0, eFapiaoBillInvoiceCheck.getAmountWithTax().compareTo(eFapiaoBillInvoice.getAmountWithTax()));
        assertEquals(eFapiaoBillInvoiceCheck.getInvoiceNo(), eFapiaoBillInvoice.getInvoiceNo());
        assertEquals(eFapiaoBillInvoiceCheck.getInvoiceCode(), eFapiaoBillInvoice.getInvoiceCode());
        assertEquals(eFapiaoBillInvoiceCheck.getPaperDrewDate(), eFapiaoBillInvoice.getPaperDrewDate());

        List<EFapiaoSyncBill> eFapiaoSyncBills = eFapiaoSyncRepository.findAll();
        eFapiaoSyncBills.forEach(e -> {
            if (mainVo.getSalesbillNo().equals(e.getSalesBillNo())) {
                assertEquals(InvoiceIssueStatus.INVOICE_CANCEL, e.getInvoiceIssueStatus());
            }
        });
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：开票状态正常，发票状态正常")
    void _10_excelImportInvoiceWriteBack_invoice_state_normal_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceType(InvoiceTypeEnum.QS.getCode());

        CallBackKerryInvoiceDetailVo detailVo = new CallBackKerryInvoiceDetailVo();
        detailVo.setGoodsTaxNo("3040502020200000000");
        detailVo.setLeaseTermStart("20240829");
        detailVo.setLeaseTermEnd("20240830");
        mainVo.setDetailVos(List.of(detailVo));

        // 执行待测代码
        eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);

        // 功能验证
        Iterable<EFapiaoBillInvoice> eFapiaoBillInvoiceIterable = eFapiaoBillInvoiceRepository.findAll(
                QEFapiaoBillInvoice.eFapiaoBillInvoice.salesBillNo.eq(mainVo.getSalesbillNo())
        );
        EFapiaoBillInvoice eFapiaoBillInvoice = eFapiaoBillInvoiceIterable.iterator().next();
        assertEquals(InvoiceTypeEnum.QS.getKipCode(), eFapiaoBillInvoice.getInvoiceType());
        assertEquals(InvoiceRecordStatus.COMPLETED, eFapiaoBillInvoice.getInvoiceRecordStatus());
        assertEquals(InvoiceState.NORMAL, eFapiaoBillInvoice.getState());
        assertEquals("IMP", eFapiaoBillInvoice.getBizType());
        assertEquals(detailVo.getLeaseTermStart(), eFapiaoBillInvoice.getLeaseTermStart());
        assertEquals(detailVo.getLeaseTermEnd(), eFapiaoBillInvoice.getLeaseTermEnd());

        checkInvoiceInfo(mainVo, eFapiaoBillInvoice);
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：开票状态正常，发票状态蓝票被红冲")
    void _11_excelImportInvoiceWriteBack_invoice_status_set_red_success() {
        List<EFapiaoBillInvoice> eFapiaoBillInvoiceOrigins = eFapiaoBillInvoiceRepository.findAll();

        Optional<EFapiaoBillInvoice> eFapiaoBillInvoiceOriginOptional =
                eFapiaoBillInvoiceOrigins.stream().filter(e -> "IMP".equals(e.getBizType()))
                                         .max(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime));
        EFapiaoBillInvoice eFapiaoBillInvoiceOrigin = eFapiaoBillInvoiceOriginOptional.get();

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(eFapiaoBillInvoiceOrigin.getSalesBillNo());
        mainVo.setInvoiceNo(eFapiaoBillInvoiceOrigin.getInvoiceNo());
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_RED_STATUS.getIndexStr());

        // 执行待测代码
        eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);

        // 功能验证
        Iterable<EFapiaoBillInvoice> eFapiaoBillInvoicesIterable = eFapiaoBillInvoiceRepository.findAll(
                QEFapiaoBillInvoice.eFapiaoBillInvoice.salesBillNo.eq(eFapiaoBillInvoiceOrigin.getSalesBillNo())
        );
        EFapiaoBillInvoice eFapiaoBillInvoice = eFapiaoBillInvoicesIterable.iterator().next();

        assertEquals(mainVo.getSalesbillNo(), eFapiaoBillInvoice.getSalesBillNo());
        assertEquals(mainVo.getInvoiceNo(), eFapiaoBillInvoice.getInvoiceNo());
        assertEquals(eFapiaoBillInvoiceOrigin.getInvoiceType(), eFapiaoBillInvoice.getInvoiceType());
        assertEquals(InvoiceRecordStatus.COMPLETED, eFapiaoBillInvoice.getInvoiceRecordStatus());
        assertEquals(InvoiceState.RED_STATUS, eFapiaoBillInvoice.getState());
        assertEquals("IMP", eFapiaoBillInvoice.getBizType());
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：开票状态正常，发票状态为红冲发票")
    void _12_excelImportInvoiceWriteBack_invoice_state_red_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_RED_INVOICE.getIndexStr());
        mainVo.setInvoiceType(InvoiceTypeEnum.QS.getCode());

        // 执行待测代码
        eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);

        // 功能验证
        Iterable<EFapiaoBillInvoice> eFapiaoBillInvoiceIterable = eFapiaoBillInvoiceRepository.findAll(
                QEFapiaoBillInvoice.eFapiaoBillInvoice.salesBillNo.eq(mainVo.getSalesbillNo())
        );

        EFapiaoBillInvoice eFapiaoBillInvoice = eFapiaoBillInvoiceIterable.iterator().next();

        assertEquals(InvoiceTypeEnum.QS.getKipCode(), eFapiaoBillInvoice.getInvoiceType());
        assertEquals(InvoiceRecordStatus.COMPLETED, eFapiaoBillInvoice.getInvoiceRecordStatus());
        assertEquals(InvoiceState.RED_LETTER, eFapiaoBillInvoice.getState());
        assertEquals("IMP", eFapiaoBillInvoice.getBizType());

        checkInvoiceInfo(mainVo, eFapiaoBillInvoice);
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：开票状态正常，发票状态为蓝票作废")
    void _13_excelImportInvoiceWriteBack_invoice_status_cancel_success() {
        List<EFapiaoBillInvoice> eFapiaoBillInvoiceOrigins = eFapiaoBillInvoiceRepository.findAll();
        Optional<EFapiaoBillInvoice> eFapiaoBillInvoiceOriginOptional = eFapiaoBillInvoiceOrigins
                .stream().filter(e -> "IMP".equals(e.getBizType()))
                .max(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime));
        EFapiaoBillInvoice eFapiaoBillInvoiceOrigin = eFapiaoBillInvoiceOriginOptional.get();

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo(eFapiaoBillInvoiceOrigin.getSalesBillNo());
        mainVo.setInvoiceNo(eFapiaoBillInvoiceOrigin.getInvoiceNo());
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_CANCEL);

        // 执行待测代码
        eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);

        // 功能验证
        List<EFapiaoBillInvoice> eFapiaoBillInvoices = eFapiaoBillInvoiceRepository.findAll();
        Optional<EFapiaoBillInvoice> eFapiaoBillInvoiceOptional = eFapiaoBillInvoices.stream()
                .max(Comparator.comparing(EFapiaoBillInvoice::getCreatedTime));
        EFapiaoBillInvoice eFapiaoBillInvoice = eFapiaoBillInvoiceOptional.get();

        assertEquals(mainVo.getSalesbillNo(), eFapiaoBillInvoice.getSalesBillNo());
        assertEquals(mainVo.getInvoiceNo(), eFapiaoBillInvoice.getInvoiceNo());
        assertEquals(eFapiaoBillInvoiceOrigin.getInvoiceType(), eFapiaoBillInvoice.getInvoiceType());
        assertEquals(InvoiceRecordStatus.COMPLETED, eFapiaoBillInvoice.getInvoiceRecordStatus());
        assertEquals(InvoiceState.CANCELLED, eFapiaoBillInvoice.getState());
        assertEquals("IMP", eFapiaoBillInvoice.getBizType());
    }

    private void checkJdeBillInfo(CallBackKerryInvoiceMainVo mainVoOrigin, EFapiaoBillInvoice eFapiaoInvoiceCheck) {
        String[] jdeSalesbillNoArray = splitJdeSalesbillNo(mainVoOrigin.getSalesbillNo());
        assertEquals(jdeSalesbillNoArray[0], eFapiaoInvoiceCheck.getKco());
        assertEquals(jdeSalesbillNoArray[1], eFapiaoInvoiceCheck.getBillType());
        assertEquals(Integer.parseInt(jdeSalesbillNoArray[2]), eFapiaoInvoiceCheck.getDoc());
        assertEquals(jdeSalesbillNoArray[3], eFapiaoInvoiceCheck.getPaymentItem());

        assertEquals(mainVoOrigin.getBusinessType(), eFapiaoInvoiceCheck.getMcu());
        assertEquals(mainVoOrigin.getExt1(), eFapiaoInvoiceCheck.getJdeUnit());
        assertEquals(mainVoOrigin.getExt2(), eFapiaoInvoiceCheck.getDoco());
        assertEquals(mainVoOrigin.getExt3(), eFapiaoInvoiceCheck.getAn8());

        assertEquals(mainVoOrigin.getSellerNo(), eFapiaoInvoiceCheck.getCompanyCode());
        assertEquals(mainVoOrigin.getPurchaserName(), eFapiaoInvoiceCheck.getPurchaserName());
        assertEquals(mainVoOrigin.getPurchaserTaxNo(), eFapiaoInvoiceCheck.getPurchaserTaxNo());
        assertEquals(mainVoOrigin.getPurchaserAddress(), eFapiaoInvoiceCheck.getPurchaserAddress());
        assertEquals(mainVoOrigin.getPurchaserBankName(), eFapiaoInvoiceCheck.getPurchaserBankName());
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getMailingAddress()));

        mainVoOrigin.getDetailVos().forEach(detailVo -> {
            assertEquals(detailVo.getGoodsTaxNo(), eFapiaoInvoiceCheck.getGoodsTaxNo());
            assertEquals(detailVo.getTaxPre(), eFapiaoInvoiceCheck.getTaxDiscount());
            assertEquals(detailVo.getTaxPreCon(), eFapiaoInvoiceCheck.getTaxDiscountDesc());
            assertEquals(detailVo.getItemName(), eFapiaoInvoiceCheck.getItemName());
            assertEquals(detailVo.getItemSpec(), eFapiaoInvoiceCheck.getItemSpec());
        });

        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getExtendedDoc()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getBillDrawDate()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getSecondName()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getFormatedDoco()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getIsContactPerson()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getIsTaxpayer()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getTaxClassifyDesc()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getUserName()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getProgramId()));
        assertEquals(BigDecimal.ZERO.intValue(), eFapiaoInvoiceCheck.getCreateDateJde());
        assertEquals(BigDecimal.ZERO.intValue(), eFapiaoInvoiceCheck.getCreateTimeJde());
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getEvent1()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getEvent2()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getEvent3()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getDscrp1()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getDscrp2()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getDscrp3()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getConstant1()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getConstant2()));
        assertEquals(0, BigDecimal.ZERO.compareTo(eFapiaoInvoiceCheck.getConstant3()));
        assertEquals(0, BigDecimal.ZERO.compareTo(eFapiaoInvoiceCheck.getConstant4()));
        assertEquals(0, BigDecimal.ZERO.compareTo(eFapiaoInvoiceCheck.getConstant5()));
        assertTrue(StringUtils.isEmpty(eFapiaoInvoiceCheck.getConstant6()));
    }

    private void checkInvoiceInfo(CallBackKerryInvoiceMainVo mainVoOrigin, EFapiaoBillInvoice eFapiaoInvoiceCheck) {
        assertEquals(mainVoOrigin.getSalesbillNo(), eFapiaoInvoiceCheck.getSalesBillNo());
        assertEquals(mainVoOrigin.getSellerName(), eFapiaoInvoiceCheck.getSellerName());
        assertEquals(mainVoOrigin.getPdfUrl(), eFapiaoInvoiceCheck.getPdfUrl());
        assertEquals(mainVoOrigin.getXmlUrl(), eFapiaoInvoiceCheck.getXmlUrl());
        assertEquals(mainVoOrigin.getOfdUrl(), eFapiaoInvoiceCheck.getOfdUrl());
        assertEquals(mainVoOrigin.getMakingReason(), eFapiaoInvoiceCheck.getMakingReason());
        assertEquals(0, mainVoOrigin.getAmountWithoutTax().compareTo(eFapiaoInvoiceCheck.getAmountWithoutTax()));
        assertEquals(0, mainVoOrigin.getTaxAmount().compareTo((eFapiaoInvoiceCheck.getTaxAmount())));
        assertEquals(0, mainVoOrigin.getAmountWithTax().compareTo(eFapiaoInvoiceCheck.getAmountWithTax()));
        assertEquals(0, new BigDecimal(mainVoOrigin.getTaxRate()).compareTo(eFapiaoInvoiceCheck.getTaxRate()));
        assertEquals(mainVoOrigin.getInvoiceNo(), eFapiaoInvoiceCheck.getInvoiceNo());
        assertEquals(mainVoOrigin.getInvoiceCode(), eFapiaoInvoiceCheck.getInvoiceCode());
        assertEquals(mainVoOrigin.getPaperDrewDate(), eFapiaoInvoiceCheck.getPaperDrewDate().replace("-", ""));
    }

    private String getRandomString() {
        return random.nextInt() + StringUtils.EMPTY;
    }

    private EFapiaoJDEBill getRandomEFapiaoJDEBill() {
        String[] invoiceTypes = new String[]{"N", "NE", "NQ", "Y", "NQ", "YQ"};
        int randomIndex = random.nextInt(invoiceTypes.length);

        return EFapiaoJDEBill.builder()
                .kco(getRandomString())
                .companyCode(getRandomString())
                .doc(random.nextInt())
                .paymentItem(getRandomString())
                .an8(getRandomString())
                .purchaserName(getRandomString())
                .purchaserTaxNo(getRandomString())
                .purchaserAddress(getRandomString())
                .purchaserBankName(getRandomString())
                .invoiceType(invoiceTypes[randomIndex])
                .mailingAddress(getRandomString())
                .mcu(getRandomString())
                .jdeUnit(getRandomString())
                .billType(getRandomString())
                .doco(getRandomString())
                .billCode(getRandomString())
                .goodsTaxNo(getRandomString())
                .itemName(getRandomString())
                .itemSpec(getRandomString())
                .amountWithoutTax(BigDecimal.valueOf(random.nextInt()))
                .taxRate(BigDecimal.valueOf(random.nextInt()))
                .taxAmount(BigDecimal.valueOf(random.nextInt()))
                .amountWithTax(BigDecimal.valueOf(random.nextInt()))
                .taxDiscount(getRandomString())
                .taxDiscountDesc(getRandomString())
                .extendedDoc(getRandomString())
                .billDrawDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .secondName(getRandomString())
                .formatedDoco(getRandomString())
                .isContactPerson(getRandomString())
                .isTaxpayer(getRandomString())
                .taxClassifyDesc(getRandomString())
                .userName(getRandomString())
                .programId(getRandomString())
                .realEstateNo(getRandomString())
                .realEstatePlace(getRandomString())
                .createDateJde(random.nextInt())
                .createTimeJde(random.nextInt())
                .event1(getRandomString())
                .event2(getRandomString())
                .event3(getRandomString())
                .dscrp1(getRandomString())
                .dscrp2(getRandomString())
                .dscrp3(getRandomString())
                .constant1(getRandomString())
                .constant2(getRandomString())
                .constant3(new BigDecimal(random.nextInt()))
                .constant4(new BigDecimal(random.nextInt()))
                .constant5(new BigDecimal(random.nextInt()))
                .constant6(getRandomString())
                .build();
    }

    private void clearData() {
        eFapiaoBillInvoiceRepository.deleteAll();
    }
}

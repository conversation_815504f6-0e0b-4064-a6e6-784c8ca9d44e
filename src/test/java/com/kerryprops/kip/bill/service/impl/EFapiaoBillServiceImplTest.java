package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.InvoiceIssueStatus;
import com.kerryprops.kip.bill.common.enums.InvoiceRedflagStatusEnum;
import com.kerryprops.kip.bill.common.enums.InvoiceState;
import com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.InvoiceUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.KerryInvoiceProperties;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSyncRepository;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.dao.entity.EFapiaoSyncBill;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.service.EFapiaoSendService;
import com.kerryprops.kip.bill.utils.InvoiceTestUtils;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceDetailVo;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Random;

import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.CE;
import static com.kerryprops.kip.bill.common.enums.InvoiceTypeEnum.QC;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomString;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * EFapiaoBillServiceImplTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-06-04
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("B端-EFapiao-单元测试")
class EFapiaoBillServiceImplTest {

    private final Random random = new Random();

    @Mock
    private KerryInvoiceProperties kerryInvoiceProperties;

    @Mock
    private KipInvoiceClient kipInvoiceClient;

    @Mock
    private EFapiaoSyncRepository eFapiaoSyncRepository;

    @Mock
    private EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @Mock
    private EFapiaoJdeBillServiceImpl eFapiaoJdeBillService;

    @Mock
    private EFapiaoSendService eFapiaoSendService;

    @InjectMocks
    private EFapiaoBillServiceImpl eFapiaoBillService;

    @Test
    @DisplayName("提交开票请求：异常场景，入参为null")
    void invoiceUpload_param_is_null_exception() {
        assertThrows(RuntimeException.class, () -> eFapiaoBillService.invoiceUpload(null));
    }

    @Test
    @DisplayName("提交开票请求：异常场景，开票请求响应异常")
    void invoiceUpload_response_fail() {
        // Arrange
        RespWrapVo<Object> respWrapVo = new RespWrapVo();
        respWrapVo.setCode(RespCodeEnum.NOT_FOUND.getCode());

        String mockGoodsTaxPrefix = "*********";
        ReflectionTestUtils.setField(eFapiaoBillService, "specialInvoiceLeaseGoodsTaxPrefix", mockGoodsTaxPrefix);

        doReturn(respWrapVo).when(kipInvoiceClient)
                            .invoiceUpload(ArgumentMatchers.any());
        doReturn("mockSystemId").when(kerryInvoiceProperties)
                                .getSystemId();

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(EFapiaoJDEBill.class), any()))
                          .thenReturn(new EFapiaoSyncBill());
            // Act and Assert
            assertThrows(RuntimeException.class, () -> eFapiaoBillService.invoiceUpload(getRandomEFapiaoJDEBill()));
        }
    }

    @Test
    @DisplayName("提交开票请求：正常场景")
    void invoiceUpload_response_success() {
        // Arrange
        RespWrapVo respWrapVo = new RespWrapVo();
        respWrapVo.setCode(RespCodeEnum.SUCCESS.getCode());
        doReturn(respWrapVo).when(kipInvoiceClient)
                            .invoiceUpload(ArgumentMatchers.any());

        doNothing().when(eFapiaoJdeBillService)
                   .writeBackUploaded(ArgumentMatchers.any());

        String mockGoodsTaxPrefix = "*********";
        ReflectionTestUtils.setField(eFapiaoBillService, "specialInvoiceLeaseGoodsTaxPrefix", mockGoodsTaxPrefix);

        EFapiaoJDEBill eFapiaoJDEBill = getRandomEFapiaoJDEBill();
        eFapiaoJDEBill.setInvoiceType("NQ");
        eFapiaoJDEBill.setDoco(randomString(200));
        eFapiaoJDEBill.setGoodsTaxNo(mockGoodsTaxPrefix + "********");
        eFapiaoJDEBill.setPurchaserBankName("name-account");
        eFapiaoJDEBill.setItemSpec("2015/07/01-2015/09/30");

        // Act and assert
        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(EFapiaoJDEBill.class), any()))
                          .thenReturn(new EFapiaoSyncBill());

            // Act and assert
            assertDoesNotThrow(() -> eFapiaoBillService.invoiceUpload(eFapiaoJDEBill));
        }
    }

    @Test
    @DisplayName("开票信息回调：正常场景：开票状态失败")
    void invoiceWriteBack_invoice_record_status_fail() {
        EFapiaoJDEBill eFapiaoJdeBill = getRandomEFapiaoJDEBill();
        eFapiaoJdeBill.setInvoiceType("NE");

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceType(QC.getCode());
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_FAILED);
        mainVo.setMessage(randomString());
        mainVo.setBusinessType(StringUtils.EMPTY);
        mainVo.setExt1(StringUtils.EMPTY);
        mainVo.setExt2(StringUtils.EMPTY);
        mainVo.setExt3(StringUtils.EMPTY);

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            // Act
            eFapiaoBillService.invoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoSyncRepository, times(1)).updateStateBySalesBillNo(eq(mainVo.getSalesbillNo()),
                                                                             eq(InvoiceIssueStatus.INVOICE_FAILED.getIndex()));

        }
    }

    @Test
    @DisplayName("开票信息回调：正常场景")
    void invoiceWriteBack_invoice_state_normal_success() {
        EFapiaoJDEBill eFapiaoJdeBill = getRandomEFapiaoJDEBill();
        eFapiaoJdeBill.setInvoiceType("NE");

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setTaxRate("0.05,0.06");
        mainVo.setInvoiceType(CE.getCode());
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr());

        // Act
        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            eFapiaoBillService.invoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoSyncRepository, times(1)).updateStateBySalesBillNo(eq(mainVo.getSalesbillNo()),
                                                                             eq(InvoiceIssueStatus.INVOICE_SUCCESS.getIndex()));
            verify(eFapiaoBillInvoiceRepository, times(1)).save(any());
        }

    }

    @Test
    @DisplayName("开票信息回调：正常场景，合并开票")
    void invoiceWriteBack_invoice_state_normal_merge_success() {
        EFapiaoJDEBill eFapiaoJdeBill = getRandomEFapiaoJDEBill();
        eFapiaoJdeBill.setInvoiceType("NE");

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setTaxRate("0.05,0.06");
        mainVo.setInvoiceType(CE.getCode());
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setSalesbillNumber(2);

        mockDisplayEFapiaoBillInvoice();

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            // Act
            eFapiaoBillService.invoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoSyncRepository, times(1)).updateStateBySalesBillNo(eq(mainVo.getSalesbillNo()),
                                                                             eq(InvoiceIssueStatus.INVOICE_SUCCESS.getIndex()));
            verify(eFapiaoBillInvoiceRepository, times(2)).save(any(EFapiaoBillInvoice.class));
        }

    }

    @Test
    @DisplayName("开票信息回调：正常场景，红冲发票第一次回调")
    void invoiceWriteBack_invoice_state_red_status_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_RED_STATUS.getIndexStr());

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            // Act
            eFapiaoBillService.invoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoSyncRepository, times(1)).updateStateBySalesBillNo(eq(mainVo.getSalesbillNo()),
                                                                             eq(InvoiceIssueStatus.INVOICE_SUCCESS_RED_STATUS.getIndex()));
            verify(eFapiaoBillInvoiceRepository, times(1)).updateStateBySalesBillNoAndInvoiceNo(any(), any(),
                                                                                                eq(InvoiceState.RED_STATUS.getIndex()));
        }

    }

    @Test
    @DisplayName("开票信息回调：正常场景，红冲发票第二次回调")
    void invoiceWriteBack_invoice_state_red_flag_success() {
        EFapiaoJDEBill eFapiaoJdeBill = getRandomEFapiaoJDEBill();
        eFapiaoJdeBill.setInvoiceType("NE");

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceType(QC.getCode());
        mainVo.setAmountWithoutTax(BigDecimal.valueOf(-1 - random.nextInt(100000)));
        mainVo.setAmountWithTax(BigDecimal.valueOf(-1 - random.nextInt(100000)));
        mainVo.setTaxAmount(BigDecimal.valueOf(-1 - random.nextInt(100000)));
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_RED_INVOICE.getIndexStr());

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            // Act
            eFapiaoBillService.invoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoSyncRepository, times(1)).updateStateBySalesBillNo(eq(mainVo.getSalesbillNo()),
                                                                             eq(InvoiceIssueStatus.INVOICE_SUCCESS_RED_FLAG.getIndex()));
            verify(eFapiaoBillInvoiceRepository, times(1)).save(any(EFapiaoBillInvoice.class));
        }
    }

    @Test
    @DisplayName("开票信息回调：正常场景，红冲发票第二次回调，合并场景")
    void invoiceWriteBack_invoice_state_red_flag_merge_success() {
        EFapiaoJDEBill eFapiaoJdeBill = getRandomEFapiaoJDEBill();
        eFapiaoJdeBill.setInvoiceType("NE");

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceType(QC.getCode());
        mainVo.setAmountWithoutTax(BigDecimal.valueOf(-1 - random.nextInt(100000)));
        mainVo.setAmountWithTax(BigDecimal.valueOf(-1 - random.nextInt(100000)));
        mainVo.setTaxAmount(BigDecimal.valueOf(-1 - random.nextInt(100000)));
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_RED_INVOICE.getIndexStr());
        mainVo.setSalesbillNumber(2);

        mockDisplayEFapiaoBillInvoice();

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            // Act
            eFapiaoBillService.invoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoSyncRepository, times(1)).updateStateBySalesBillNo(eq(mainVo.getSalesbillNo()),
                                                                             eq(InvoiceIssueStatus.INVOICE_SUCCESS_RED_FLAG.getIndex()));
            verify(eFapiaoBillInvoiceRepository, times(2)).save(any(EFapiaoBillInvoice.class));
        }
    }


    @Test
    @DisplayName("开票信息回调：作废场景")
    void invoiceWriteBack_invoice_status_cancel_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_CANCEL);

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            // Act
            eFapiaoBillService.invoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoSyncRepository, times(1)).updateStateBySalesBillNo(eq(mainVo.getSalesbillNo()),
                                                                             eq(InvoiceIssueStatus.INVOICE_CANCEL.getIndex()));
            verify(eFapiaoBillInvoiceRepository, times(1)).updateStateBySalesBillNoAndInvoiceNo(any(), any(),
                                                                                                eq(InvoiceState.CANCELLED.getIndex()));

        }
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：异常场景，入参为空")
    void excelImportInvoiceWriteBack_invoiceMainVoNullError() {
        assertDoesNotThrow(() -> eFapiaoBillService.excelImportInvoiceWriteBack(null));
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：an8为空")
    void excelImportInvoiceWriteBack_an8EmptySuccess() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceType(InvoiceTypeEnum.QS.getCode());

        mainVo.setBusinessType(randomString());
        mainVo.setExt2(randomString());
        mainVo.setExt3(StringUtils.EMPTY); // an8为空

        // Act and Assert
        assertDoesNotThrow(() -> eFapiaoBillService.excelImportInvoiceWriteBack(mainVo));
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：开票状态正常，发票状态正常")
    void excelImportInvoiceWriteBack_invoice_state_normal_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceType(InvoiceTypeEnum.QS.getCode());

        CallBackKerryInvoiceDetailVo detailVo = new CallBackKerryInvoiceDetailVo();
        detailVo.setGoodsTaxNo("*********0200000000");
        detailVo.setLeaseTermStart("20240829");
        detailVo.setLeaseTermEnd("20240830");
        mainVo.setDetailVos(List.of(detailVo));

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());

            // Act
            eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoBillInvoiceRepository, times(1)).save(any(EFapiaoBillInvoice.class));
        }
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：开票状态正常，发票状态正常，合并场景")
    void excelImportInvoiceWriteBack_invoice_state_normal_merge_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_DEFAULT.getIndexStr());
        mainVo.setInvoiceType(InvoiceTypeEnum.QS.getCode());
        mainVo.setSalesbillNumber(2);

        CallBackKerryInvoiceDetailVo detailVo = new CallBackKerryInvoiceDetailVo();
        detailVo.setGoodsTaxNo("*********0200000000");
        detailVo.setLeaseTermStart("20240829");
        detailVo.setLeaseTermEnd("20240830");
        mainVo.setDetailVos(List.of(detailVo));

        mockDisplayEFapiaoBillInvoice();

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());

            // Act
            eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoBillInvoiceRepository, times(2)).save(any(EFapiaoBillInvoice.class));
        }
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：开票状态正常，发票状态蓝票被红冲")
    void excelImportInvoiceWriteBack_invoice_status_set_red_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_RED_STATUS.getIndexStr());

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            // Act
            eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoBillInvoiceRepository, times(1)).updateStateBySalesBillNoAndInvoiceNo(any(), any(),
                                                                                                eq(InvoiceState.RED_STATUS.getIndex()));

        }
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：开票状态正常，发票状态为红冲发票")
    void excelImportInvoiceWriteBack_invoice_state_red_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_RED_INVOICE.getIndexStr());
        mainVo.setInvoiceType(InvoiceTypeEnum.QS.getCode());

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            // Act
            eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoBillInvoiceRepository, times(1)).save(any(EFapiaoBillInvoice.class));

        }
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：开票状态正常，发票状态为红冲发票，合并场景")
    void excelImportInvoiceWriteBack_invoice_state_red_merge_success() {
        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_NORMAL);
        mainVo.setRedFlag(InvoiceRedflagStatusEnum.RED_FLAG_RED_INVOICE.getIndexStr());
        mainVo.setInvoiceType(InvoiceTypeEnum.QS.getCode());
        mainVo.setSalesbillNumber(2);

        mockDisplayEFapiaoBillInvoice();

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            // Act
            eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoBillInvoiceRepository, times(2)).save(any(EFapiaoBillInvoice.class));

        }
    }

    @Test
    @DisplayName("页面导入开票场景，开票信息回调：开票状态正常，发票状态为蓝票作废")
    void excelImportInvoiceWriteBack_invoice_status_cancel_success() {

        CallBackKerryInvoiceMainVo mainVo = InvoiceTestUtils.getCallBackKerryInvoiceMainVo();
        mainVo.setInvoiceStatus(InvoiceUtils.INVOICE_STATUS_SUCCESS);
        mainVo.setStatus(InvoiceUtils.STATUS_CANCEL);

        try (MockedStatic<BeanUtil> mockedBeanUtil = Mockito.mockStatic(BeanUtil.class)) {
            mockedBeanUtil.when(() -> BeanUtil.copy(any(CallBackKerryInvoiceMainVo.class), any()))
                          .thenReturn(new EFapiaoBillInvoice());
            // Act
            eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);

            // Assert
            verify(eFapiaoBillInvoiceRepository, times(1)).updateStateBySalesBillNoAndInvoiceNo(any(), any(),
                                                                                                eq(InvoiceState.CANCELLED.getIndex()));

        }

    }

    @ParameterizedTest
    @CsvSource({"N, gvat", "NE, gvate", "NQ, gvatq", "Y, svat", "YQ, svatq", "YE, svate"
    })
    @DisplayName("获取发票类型KipCode，正常场景")
    void getInvoiceTypeKipCode_success(String invoiceTypeJde, String expectedKipCode) {
        // Act
        String invoiceTypeKipCode =
                ReflectionTestUtils.invokeMethod(eFapiaoBillService, "getInvoiceTypeKipCode", invoiceTypeJde);

        // Assert
        assertEquals(expectedKipCode, invoiceTypeKipCode);
    }

    @Test
    @DisplayName("获取发票类型KipCode，异常场景：未知类型")
    void getInvoiceTypeKipCode_unknownType() {
        // Arrange
        String invoiceTypeJde = "UNKNOWN";

        // Act
        String invoiceTypeKipCode =
                ReflectionTestUtils.invokeMethod(eFapiaoBillService, "getInvoiceTypeKipCode", invoiceTypeJde);

        // Assert
        assertTrue(StringUtils.isEmpty(invoiceTypeKipCode));
    }

    @Test
    @DisplayName("分隔购方名称和账号，正常场景")
    void splitNameAccount_success() {
        // Arrange
        String nameAccount = "花旗银行北京分行**********";

        // Act
        Object nameAndAccount = ReflectionTestUtils.invokeMethod(eFapiaoBillService, "splitNameAccount", nameAccount);

        // Assert
        assertNotNull(nameAndAccount);
    }

    @Test
    @DisplayName("分隔购方名称和账号，异常场景：输入为空")
    void splitNameAccount_emptyInput() {
        // Arrange
        String nameAccount = "";

        // Act
        Object nameAndAccount = ReflectionTestUtils.invokeMethod(eFapiaoBillService, "splitNameAccount", nameAccount);

        // Assert
        assertNotNull(nameAndAccount);
    }

    @Test
    @DisplayName("分隔购方名称和账号，异常场景：无购方账号（数字）部分")
    void splitNameAccount_noAccount() {
        // Arrange
        String nameAccount = "花旗银行北京分行";

        // Act
        Object nameAndAccount = ReflectionTestUtils.invokeMethod(eFapiaoBillService, "splitNameAccount", nameAccount);


        // Assert
        assertNotNull(nameAndAccount);
    }

    @Test
    @DisplayName("分隔购方名称和账号，异常场景：无购方名称部门部分")
    void splitNameAccount_noName() {
        // Arrange
        String nameAccount = "**********";

        // Act
        Object nameAndAccount = ReflectionTestUtils.invokeMethod(eFapiaoBillService, "splitNameAccount", nameAccount);

        // Assert
        assertNotNull(nameAndAccount);
    }

    private EFapiaoJDEBill getRandomEFapiaoJDEBill() {
        String[] invoiceTypes = new String[]{"N", "NE", "NQ", "Y", "NQ", "YQ"};
        int randomIndex = random.nextInt(invoiceTypes.length);

        return EFapiaoJDEBill.builder()
                             .kco(randomString())
                             .companyCode(randomString())
                             .doc(random.nextInt())
                             .paymentItem(randomString())
                             .an8(randomString())
                             .purchaserName(randomString())
                             .purchaserTaxNo(randomString())
                             .purchaserAddress(randomString())
                             .purchaserBankName(randomString())
                             .invoiceType(invoiceTypes[randomIndex])
                             .mailingAddress(randomString())
                             .mcu(randomString())
                             .jdeUnit(randomString())
                             .billType(randomString())
                             .doco(randomString())
                             .billCode(randomString())
                             .goodsTaxNo(randomString())
                             .itemName(randomString())
                             .itemSpec(randomString())
                             .amountWithoutTax(BigDecimal.valueOf(random.nextInt()))
                             .taxRate(BigDecimal.valueOf(random.nextInt()))
                             .taxAmount(BigDecimal.valueOf(random.nextInt()))
                             .amountWithTax(BigDecimal.valueOf(random.nextInt()))
                             .taxDiscount(randomString())
                             .taxDiscountDesc(randomString())
                             .extendedDoc(randomString())
                             .billDrawDate(LocalDateTime.now()
                                                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                             .secondName(randomString())
                             .formatedDoco(randomString())
                             .isContactPerson(randomString())
                             .isTaxpayer(randomString())
                             .taxClassifyDesc(randomString())
                             .userName(randomString())
                             .programId(randomString())
                             .realEstateNo(randomString())
                             .realEstatePlace(randomString())
                             .createDateJde(random.nextInt())
                             .createTimeJde(random.nextInt())
                             .event1(randomString())
                             .event2(randomString())
                             .event3(randomString())
                             .dscrp1(randomString())
                             .dscrp2(randomString())
                             .dscrp3(randomString())
                             .constant1(randomString())
                             .constant2(randomString())
                             .constant3(new BigDecimal(random.nextInt()))
                             .constant4(new BigDecimal(random.nextInt()))
                             .constant5(new BigDecimal(random.nextInt()))
                             .constant6(randomString())
                             .build();
    }

    private void mockDisplayEFapiaoBillInvoice() {
        var mockInvoice = new EFapiaoBillInvoice();
        mockInvoice.setMcu("mockMcu");
        mockInvoice.setJdeUnit("mockJdeUnit");

        JPAQueryFactory mockQueryFactory = mock(JPAQueryFactory.class);
        JPAQuery mockQuery = mock(JPAQuery.class);

        when(eFapiaoBillInvoiceRepository.getJpaQueryFactory()).thenReturn(mockQueryFactory);
        when(mockQueryFactory.selectFrom(any(EntityPath.class))).thenReturn(mockQuery);
        when(mockQuery.where(any(Predicate.class))).thenReturn(mockQuery);
        when(mockQuery.fetchFirst()).thenReturn(mockInvoice);
    }

}
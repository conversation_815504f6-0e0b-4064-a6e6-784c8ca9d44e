package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.MsgCallbackProperties;
import com.kerryprops.kip.bill.dao.EFapiaoBillInvoiceRepository;
import com.kerryprops.kip.bill.dao.EFapiaoSendRecordRepository;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.EmailReplyVo;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommandV2;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.thymeleaf.TemplateEngine;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.SUCCESS;
import static com.kerryprops.kip.bill.common.enums.SendStatus.MSG_SENDING;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomObject;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anySet;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * EFapiaoSendServiceImplTest.
 *
 * <AUTHOR> Yu 2024-11-15 17:41:39
 **/
@ExtendWith(MockitoExtension.class)
class EFapiaoSendServiceImplTest {

    @Mock
    IBillService billService;

    @Mock
    ObjectMapper objectMapper;

    @Mock
    TemplateEngine templateEngine;

    @Mock
    private MessageClient messageClient;

    @Mock
    MessageCenterClient messageCenterClient;

    @Mock
    AptInvoiceSmsService aptInvoiceSmsService;

    @Mock
    MsgCallbackProperties msgCallbackProperties;

    @Mock
    EFapiaoSendRecordRepository fapiaoSendRecordRepository;

    @Mock
    EFapiaoBillInvoiceRepository eFapiaoBillInvoiceRepository;

    @InjectMocks
    EFapiaoSendServiceImpl eFapiaoSendServiceImpl;

    @Test
    void sendInvoiceMessage_ok() {
        StaffBillReceiverRespVo staffBillReceiverRespVo = randomObject(StaffBillReceiverRespVo.class);
        staffBillReceiverRespVo.setUserId("1");
        when(billService.queryBillReceiversByDocoAn8(anyString(), anyString())).thenReturn(
                Set.of(staffBillReceiverRespVo));
        when(messageCenterClient.sendMessage(any())).thenReturn(new RespWrapVo<>("data"));
        when(eFapiaoBillInvoiceRepository.updateSendMessageStatusByIds(any(), any())).thenReturn(1);
        EFapiaoBillInvoice eFapiaoBillInvoice = randomObject(EFapiaoBillInvoice.class);
        eFapiaoBillInvoice.setInvoiceRecordStatus(InvoiceRecordStatus.COMPLETED);
        when(eFapiaoBillInvoiceRepository.findAllById(any())).thenReturn(List.of(eFapiaoBillInvoice));

        int result = eFapiaoSendServiceImpl.sendInvoiceMessage(StringUtils.EMPTY, Set.of(1L));
        assertThat(result).isEqualTo(1);
        verify(messageCenterClient, times(1)).sendMessage(any());
    }

    @DisplayName("发送通知邮件：异常场景：没有可发送发票")
    @Test
    void testNotifyByEmail_noInvoicesError() {
        // Arrange
        String projectId = "PROJECT001";
        StaffBillReceiverRespVo receiver = new StaffBillReceiverRespVo();

        EFapiaoBillInvoice invoice = new EFapiaoBillInvoice();
        invoice.setId(1L);
        invoice.setInvoiceRecordStatus(InvoiceRecordStatus.PROCESSING);
        Set<EFapiaoBillInvoice> billFapiaos = Set.of(invoice);

        Map<Long, String> batchNoMap = Map.of(1L, "BATCH001");

        // Act
        eFapiaoSendServiceImpl.notifyByEmail(projectId, receiver, billFapiaos, batchNoMap);

        // Assert
        verify(messageClient, never()).sendEmailV2(any(EmailSendCommandV2.class));
    }

    @DisplayName("发送通知邮件：异常场景：邮件发送失败")
    @Test
    void testNotifyByEmail_sendEmailError() {
        // Arrange
        String projectId = "PROJECT001";
        StaffBillReceiverRespVo receiver = new StaffBillReceiverRespVo();
        receiver.setEmail("<EMAIL>");

        EFapiaoBillInvoice invoice = new EFapiaoBillInvoice();
        invoice.setId(1L);
        invoice.setInvoiceRecordStatus(InvoiceRecordStatus.COMPLETED);
        Set<EFapiaoBillInvoice> billFapiaos = Set.of(invoice);

        Map<Long, String> batchNoMap = Map.of(1L, "BATCH001");

        var emailReplyVo = new EmailReplyVo();
        emailReplyVo.setRequestId(StringUtils.EMPTY); // 模拟发送失败的情况

        RespWrapVo<EmailReplyVo> respWrapVo = new RespWrapVo<>();
        respWrapVo.setCode(SUCCESS.getCode());
        respWrapVo.setData(emailReplyVo);
        when(messageClient.sendEmailV2(any(EmailSendCommandV2.class))).thenReturn(respWrapVo);

        // Act
        assertThrows(RuntimeException.class, () -> eFapiaoSendServiceImpl.notifyByEmail(projectId, receiver,
                                                                                        billFapiaos,
                                                                            batchNoMap));
    }

    @DisplayName("发送通知邮件：正常场景：通过邮件通知成功")
    @Test
    void testNotifyByEmail_success() {
        // Arrange
        String projectId = "PROJECT001";
        StaffBillReceiverRespVo receiver = new StaffBillReceiverRespVo();
        receiver.setEmail("<EMAIL>");
        receiver.setUserId("USER001");

        EFapiaoBillInvoice invoice = new EFapiaoBillInvoice();
        invoice.setId(1L);
        invoice.setInvoiceRecordStatus(InvoiceRecordStatus.COMPLETED);
        invoice.setPdfUrl("http://example.com/invoice1.pdf");
        invoice.setInvoiceCode("CODE001");
        invoice.setInvoiceNo("NO001");

        Set<EFapiaoBillInvoice> billFapiaos = Set.of(invoice);
        Map<Long, String> batchNoMap = Map.of(1L, "BATCH001");

        var emailReplyVo = new EmailReplyVo();
        emailReplyVo.setRequestId("REQ123");

        RespWrapVo<EmailReplyVo> respWrapVo = new RespWrapVo<>();
        respWrapVo.setCode(SUCCESS.getCode());
        respWrapVo.setData(emailReplyVo);
        when(messageClient.sendEmailV2(any(EmailSendCommandV2.class))).thenReturn(respWrapVo);

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser)
                                .thenReturn(randomLoginUser());

            // Act
            eFapiaoSendServiceImpl.notifyByEmail(projectId, receiver, billFapiaos, batchNoMap);

            // Assert
            verify(messageClient, times(1)).sendEmailV2(any(EmailSendCommandV2.class));
            verify(fapiaoSendRecordRepository, times(1)).saveAll(anyList());
            verify(eFapiaoBillInvoiceRepository, times(1)).updateSendEmailStatusByIds(anySet(), eq(MSG_SENDING));
        }
    }

    @DisplayName("异常场景：通过邮件通知，MessageClient服务异常")
    @Test
    void testNotifyByEmail_messageClientError() {
        // Arrange
        String projectId = "PROJECT001";
        var receiver = new StaffBillReceiverRespVo();

        EFapiaoBillInvoice invoice = new EFapiaoBillInvoice();
        invoice.setId(1L);
        invoice.setInvoiceRecordStatus(InvoiceRecordStatus.COMPLETED);
        Set<EFapiaoBillInvoice> billFapiaos = Set.of(invoice);

        Map<Long, String> batchNoMap = Map.of(1L, "BATCH001");

        // Act and Assert
        assertThrows(RuntimeException.class, () -> eFapiaoSendServiceImpl.notifyByEmail(projectId, receiver,
                                                                                        billFapiaos,
                                                                                        batchNoMap));
    }

    @DisplayName("获取发票接收人信息，异常场景，无开票完成账单")
    @Test
    void testInvoiceReceivers_noCompletedInvoice() {
        // Arrange
        String projectId = "PROJECT001";

        EFapiaoBillInvoice invoice = new EFapiaoBillInvoice();
        invoice.setId(1L);
        invoice.setInvoiceRecordStatus(InvoiceRecordStatus.PROCESSING);
        List<EFapiaoBillInvoice> billFapiaos = List.of(invoice);
        when(eFapiaoBillInvoiceRepository.findAllById(anySet())).thenReturn(billFapiaos);


        // Act
        Map<StaffBillReceiverRespVo, Set<EFapiaoBillInvoice>> resultMap =
                eFapiaoSendServiceImpl.invoiceReceivers(projectId, Set.of(1L));

        // Assert
        assertThat(resultMap).isEmpty();
    }


}
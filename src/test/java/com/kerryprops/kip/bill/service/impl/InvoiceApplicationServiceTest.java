package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.InvoiceRecordRepository;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.EFapiaoBillInvoice;
import com.kerryprops.kip.bill.dao.entity.InvoiceRecord;
import com.kerryprops.kip.bill.feign.entity.EmailReplyVo;
import com.kerryprops.kip.bill.feign.entity.UploadInvoiceMainV2Vo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;

import static com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus.COMPLETED;
import static com.kerryprops.kip.bill.common.enums.InvoiceRecordStatus.PROCESSING;
import static com.kerryprops.kip.bill.common.enums.InvoicedStatus.HAS_INVOICED;
import static com.kerryprops.kip.bill.utils.RandomUtil.randomLoginUser;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * InvoiceApplicationServiceTest.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Zihan Yan
 * @since - 2025-04-29
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("开票功能Service-单元测试")
class InvoiceApplicationServiceTest {

    @Mock
    private InvoiceRecordRepository invoiceRecordRepository;

    @Mock
    private AptPaymentInfoRepository aptPaymentInfoRepository;

    @Mock
    private AptPayRepository aptPayRepository;

    @Mock
    private AptInvoiceSmsService aptInvoiceSmsService;

    @InjectMocks
    private InvoiceApplicationService invoiceApplicationService;

    @DisplayName("正常场景：更新发票记录成功")
    @Test
    void testUpdateInvoiceRecord_success() {
        // Arrange
        var aptPaymentInfo = new AptPaymentInfo();
        aptPaymentInfo.setId("PAYMENT123");

        CallBackKerryInvoiceMainVo mainVo = new CallBackKerryInvoiceMainVo();
        mainVo.setSalesbillNo("ORDER123");
        mainVo.setInvoiceStatus(1);
        mainVo.setInvoiceCode("CODE001");
        mainVo.setInvoiceNo("NO001");
        mainVo.setPdfUrl("http://example.com/invoice.pdf");
        mainVo.setXmlUrl("http://example.com/invoice.xml");
        mainVo.setOfdUrl("http://example.com/invoice.ofd");
        mainVo.setAmountWithTax(BigDecimal.TEN);

        var record = new InvoiceRecord();
        record.setOrderNo("ORDER123");
        record.setInvoiceCode("OLD_CODE");
        record.setInvoiceNo("OLD_NO");
        record.setPdfUrl("http://example.com/old_invoice.pdf");
        record.setXmlUrl("http://example.com/old_invoice.xml");
        record.setOfdUrl("http://example.com/old_invoice.ofd");

        when(invoiceRecordRepository.getByOrderNo("ORDER123")).thenReturn(record);

        // Act
        invoiceApplicationService.updateInvoiceRecord(aptPaymentInfo, mainVo);

        // Assert
        verify(invoiceRecordRepository, times(1)).save(record);
        assertEquals("OLD_CODE,CODE001", record.getInvoiceCode());
        assertEquals("OLD_NO,NO001", record.getInvoiceNo());
        assertEquals("http://example.com/old_invoice.pdf,http://example.com/invoice.pdf", record.getPdfUrl());
        assertEquals("http://example.com/old_invoice.xml,http://example.com/invoice.xml", record.getXmlUrl());
        assertEquals("http://example.com/old_invoice.ofd,http://example.com/invoice.ofd", record.getOfdUrl());
        assertEquals(COMPLETED, record.getStatus());
    }

    @DisplayName("正常场景：保存发票记录成功")
    @Test
    void testSaveInvoiceRecord_success() {
        // Arrange
        var mainVo = new UploadInvoiceMainV2Vo();
        mainVo.setPurchaserName("Test Purchaser");
        mainVo.setPurchaserTaxNo("TAX123");
        mainVo.setPurchaserAddress("Test Address");
        mainVo.setPurchaserBankName("Test Bank");
        mainVo.setPurchaserBankAccount("*********");
        mainVo.setSalesbillNo("ORDER123");
        mainVo.setAmountWithTax(BigDecimal.ONE);
        mainVo.setInvoiceType("01");
        mainVo.setReceiveUserEmail("<EMAIL>");

        String sellerName = "Test Seller";
        String areaCode = "AREA001";

        try (MockedStatic<UserInfoUtils> mockedStaticUserInfo = Mockito.mockStatic(UserInfoUtils.class)) {
            mockedStaticUserInfo.when(UserInfoUtils::getUser).thenReturn(randomLoginUser());

            // Act
            InvoiceRecord result = invoiceApplicationService.saveInvoiceRecord(mainVo, sellerName, areaCode);

            // Assert
            assertNotNull(result);
            assertEquals("Test Purchaser", result.getCustomerName());
            assertEquals("Test Seller", result.getSellerName());
            assertEquals("TAX123", result.getTaxNo());
            assertEquals("Test Address", result.getAddress());
            assertEquals("Test Bank", result.getBankName());
            assertEquals("*********", result.getBankAccount());
            assertEquals("ORDER123", result.getOrderNo());
            assertEquals(PROCESSING, result.getStatus());
        }
    }

    @DisplayName("正常场景：更新已开票状态成功")
    @Test
    void testUpdateInvoicedStatus_success() {
        // Arrange
        String paymentId = "PAYMENT123";

        // Act
        invoiceApplicationService.updateInvoicedStatus(paymentId, HAS_INVOICED);

        // Assert
        verify(aptPaymentInfoRepository, times(1)).updateInvoicedStatus(anyString(), eq(HAS_INVOICED));
        verify(aptPayRepository, times(1)).updateInvoicedStatus(anyString(), eq(HAS_INVOICED));
    }

    @Test
    @DisplayName("更新发票状态，正常场景")
    void updateInvoiceInvoked_success() {
        // Arrange
        InvoiceRecord invoiceRecord = new InvoiceRecord();
        invoiceApplicationService.updateInvoiceInvoked(invoiceRecord);

        // Assert
        assertTrue(invoiceRecord.getIsInvoiceInvoked());
        verify(invoiceRecordRepository, times(1)).save(invoiceRecord);
    }

    @Test
    @DisplayName("根据订单号获取发票记录，正常场景")
    void getInvoiceRecordByOrderNo_success() {
        // Arrange
        String orderNo = "ORDER123";
        var mockRecord = new InvoiceRecord();
        mockRecord.setOrderNo(orderNo);
        when(invoiceRecordRepository.getByOrderNo(orderNo)).thenReturn(mockRecord);

        // Act
        InvoiceRecord result = invoiceApplicationService.getInvoiceRecordByOrderNo(orderNo);

        // Assert
        assertNotNull(result);
        assertEquals(orderNo, result.getOrderNo());
        verify(invoiceRecordRepository, times(1)).getByOrderNo(orderNo);
    }

    @Test
    @DisplayName("根据订单号获取发票记录，异常场景：抛出异常")
    void getInvoiceRecordByOrderNo_exception() {
        // Arrange
        String orderNo = "ORDER_EXCEPTION";
        when(invoiceRecordRepository.getByOrderNo(orderNo)).thenThrow(new RuntimeException("Database error"));

        // Act
        InvoiceRecord result = invoiceApplicationService.getInvoiceRecordByOrderNo(orderNo);

        // Assert
        assertNull(result);
        verify(invoiceRecordRepository, times(1)).getByOrderNo(orderNo);
    }

}
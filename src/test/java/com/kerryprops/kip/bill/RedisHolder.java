//package com.kerryprops.kip.bill;
//
//import org.springframework.test.util.TestSocketUtils;
//import org.springframework.util.function.SingletonSupplier;
//import redis.embedded.RedisServer;
//
//import java.io.IOException;
//
///**
// * RedisHolder.
// *
// * <AUTHOR> Yu 2024-08-07 13:52:02
// **/
//public class RedisHolder {
//
//    private static final SingletonSupplier<RedisServer> redisServerSupplier = SingletonSupplier.of(() -> createRedis());
//
//    public static void start() {
//        redisServerSupplier.obtain().start();
//    }
//
//    public static int getRedisPort() {
//        return redisServerSupplier.obtain().ports().get(0);
//    }
//
//    private static RedisServer createRedis() {
//        return RedisServer.builder()
//                          .setting("maxmemory 128M")
//                          .port(9637)
//                          .build();
//    }
//
//    public static void main(String[] args) throws IOException {
//        RedisServer redisServer = new RedisServer(6379);
//        redisServer.start();
//// do some work
//        System.out.println("Redis server is running on port: " + redisServer.ports().get(0));
//        redisServer.stop();
//    }
//
//}

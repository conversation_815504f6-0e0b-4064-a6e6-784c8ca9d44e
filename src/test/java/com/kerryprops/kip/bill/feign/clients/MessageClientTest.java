package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.BaseIntegrationTest;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.entity.EmailReplyVo;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.feign.entity.WxSubscribeSendRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.SUCCESS;
import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.UNKNOWN_ERROR;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * MessageClientTest.
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@DisplayName("ums-FeignClient验证")
class MessageClientTest extends BaseIntegrationTest {

    @Autowired
    MessageClient messageClient;

    @Test
    @DisplayName("阿里云接口发送邮件-正常场景")
    void _01_sendWithReplyAlicloud_success() {
        final String SUCCESS_FLAG = "success";
        EmailSendCommand emailSendCommand = new EmailSendCommand();
        emailSendCommand.setText(SUCCESS_FLAG);
        emailSendCommand.setSubject(SUCCESS_FLAG);

        RespWrapVo<EmailReplyVo> replyVoRespWrapVo = messageClient.sendWithReplyAlicloud(emailSendCommand);

        assertEquals(SUCCESS.getCode(), replyVoRespWrapVo.getCode());
        assertEquals(SUCCESS.getMessage(), replyVoRespWrapVo.getMessage());
        assertEquals("5514439437b9491a80b6fcb04576dd6b", replyVoRespWrapVo.getData()
                                                                          .getRequestId());
    }

    @Test
    @DisplayName("阿里云接口发送邮件-异常场景")
    void _02_sendWithReplyAlicloud_error() {
        final String ERROR_FLAG = "error";
        EmailSendCommand emailSendCommand = new EmailSendCommand();
        emailSendCommand.setText(ERROR_FLAG);
        emailSendCommand.setSubject(ERROR_FLAG);
        emailSendCommand.setSendTo(randomString());

        RespWrapVo<EmailReplyVo> replyVoRespWrapVo = messageClient.sendWithReplyAlicloud(emailSendCommand);

        assertEquals(UNKNOWN_ERROR.getCode(), replyVoRespWrapVo.getCode());
    }

    @Test
    @DisplayName("sendWxSubscribe-有效请求-正常响应")
    void sendWxSubscribe_validRequest_ok() {
        Map<String, Object> params = new HashMap<>();
        params.put("thing1", "测试小区");
        params.put("thing2", "测试单元户号");
        params.put("thing3", "管理费");
        params.put("thing5", "请及时缴纳费用，祝您生活愉快！");

        var request = new WxSubscribeSendRequest();
        request.setUserId("8aaa84cc8c86ba86018c8a3446f20000");
        request.setTemplateId("0Fu42WnHW-P0poJ4tqWeo--96KCVzntIxDV6NI1vp4I");
        request.setOpenId("o7w8q48a9BGrzxCbqVv4vOjuD1sE");
        request.setTemplateParams(params);
        // request.setRoutePage();

        var vo = messageClient.sendWxSubscribe(request);

        assertThat(vo).isNotNull();
        assertThat(vo.getData()).isTrue();
    }

}
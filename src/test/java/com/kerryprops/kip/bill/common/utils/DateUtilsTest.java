package com.kerryprops.kip.bill.common.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.NullAndEmptySource;
import org.junit.jupiter.params.provider.ValueSource;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.stream.Stream;

import static com.kerryprops.kip.bill.common.utils.DateUtils.CH_YYYY_MM_DD;
import static com.kerryprops.kip.bill.common.utils.DateUtils.YYYY_MM_DD;
import static com.kerryprops.kip.bill.common.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * DateUtils测试类
 *
 * <AUTHOR> Yan
 * @date 2024-10-12
 */
class DateUtilsTest {

    private static final String DATE_STR_YEAR_DAY_TEST = "124032"; // 儒日历

    private static final String DATE_STR_YYYYMMDD_TEST = "2024-02-01";

    @Test
    void _01_convertDateYYYYMMDD_success() {
        String dateYYYYMMDD = DateUtils.convertDateYYYYMMDD(DATE_STR_YEAR_DAY_TEST);
        assertThat(dateYYYYMMDD).isEqualTo(DATE_STR_YYYYMMDD_TEST);
    }

    @ParameterizedTest
    @NullAndEmptySource
    @ValueSource(strings = {"12x", "abcdef", "a1234567a"})
    void _01_1_convertDateYYYYMMDD_invalid_input(String input) {
        Exception exception = assertThrows(Exception.class, () -> DateUtils.convertDateYYYYMMDD(input));
        assertNotNull(exception);
    }

    @Test
    void _02_convertDateToJuLian_success() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date dateTest = sdf.parse(DATE_STR_YYYYMMDD_TEST);

        String dateStr = DateUtils.convertDateToJuLian(dateTest);
        assertThat(dateStr).isEqualTo(DATE_STR_YEAR_DAY_TEST);
    }

    @Test
    void _02_1_convertDateToJuLian_null_input() {
        assertThrows(NullPointerException.class, () -> DateUtils.convertDateToJuLian(null));
    }

    @Test
    void _03_parseDate_success() {
        Date dateActual = DateUtils.parseDate(YYYY_MM_DD, DATE_STR_YYYYMMDD_TEST);
        assertEquals(DATE_STR_YYYYMMDD_TEST, new SimpleDateFormat(YYYY_MM_DD).format(dateActual));
    }

    @Test
    void _03_1_parseDate_null_pattern() {
        Date result = DateUtils.parseDate(null, DATE_STR_YYYYMMDD_TEST);
        assertNull(result);
    }

    @Test
    void _03_2_parseDate_null_dateStr() {
        Date result = DateUtils.parseDate(YYYY_MM_DD, null);
        assertNull(result);
    }

    @Test
    void _03_3_parseDate_invalid_format() {
        Date result = DateUtils.parseDate(YYYY_MM_DD, "invalid-date");
        assertNull(result);
    }

    @Test
    void _04_getNowDate_success() {
        var currentTimeMillis = System.currentTimeMillis() / 1000;
        Date now = DateUtils.getNowDate();
        var epochSecond = now.toInstant()
                             .getEpochSecond();

        assertThat(epochSecond).isBetween(currentTimeMillis, currentTimeMillis + 3);
    }

    @Test
    void _05_getTime_success() {
        var timeStr = DateUtils.getTime();
        assertNotNull(timeStr);

        // 验证格式是否符合 yyyy-MM-dd HH:mm:ss
        assertTrue(timeStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));

        // 通过解析验证时间是否在当前时间附近
        Date parsedDate = DateUtils.parseDate(YYYY_MM_DD_HH_MM_SS, timeStr);
        assertNotNull(parsedDate);

        long currentTimeMillis = System.currentTimeMillis();
        long parsedTimeMillis = parsedDate.getTime();

        // 验证生成的时间在当前时间的5秒范围内
        assertThat(parsedTimeMillis).isBetween(currentTimeMillis - 5000, currentTimeMillis + 5000);
    }

    @Test
    void _06_dateTime_success() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date testDate = sdf.parse(DATE_STR_YYYYMMDD_TEST);

        String result = DateUtils.dateTime(testDate);
        assertEquals(DATE_STR_YYYYMMDD_TEST, result);
    }

    @Test
    void _06_1_dateTime_null_input() {
        String result = DateUtils.dateTime(null);
        assertNull(result);
    }

    @Test
    void _07_parseDateToStr_success() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date testDate = sdf.parse(DATE_STR_YYYYMMDD_TEST);

        String result = DateUtils.parseDateToStr(YYYY_MM_DD, testDate);
        assertEquals(DATE_STR_YYYYMMDD_TEST, result);
    }

    @Test
    void _07_1_parseDateToStr_with_time() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        Date testDate = sdf.parse("2024-02-01 12:34:56");

        String result = DateUtils.parseDateToStr(YYYY_MM_DD_HH_MM_SS, testDate);
        assertEquals("2024-02-01 12:34:56", result);
    }

    @Test
    void _07_2_parseDateToStr_chinese_format() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date testDate = sdf.parse(DATE_STR_YYYYMMDD_TEST);

        String result = DateUtils.parseDateToStr(CH_YYYY_MM_DD, testDate);
        assertEquals("2024年02月01日", result);
    }

    @Test
    void _07_3_parseDateToStr_null_date() {
        String result = DateUtils.parseDateToStr(YYYY_MM_DD, null);
        assertNull(result);
    }

    @Test
    void _07_4_parseDateToStr_null_pattern() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date testDate = sdf.parse(DATE_STR_YYYYMMDD_TEST);

        String result = DateUtils.parseDateToStr(null, testDate);
        assertNull(result);
    }

    @Test
    void _08_formatter_cache_reuse() throws ParseException {
        // 测试DateTimeFormatter缓存机制
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date testDate1 = sdf.parse("2024-01-01");
        Date testDate2 = sdf.parse("2024-02-01");

        // 多次使用相同格式
        String result1 = DateUtils.parseDateToStr(YYYY_MM_DD, testDate1);
        String result2 = DateUtils.parseDateToStr(YYYY_MM_DD, testDate2);

        assertEquals("2024-01-01", result1);
        assertEquals("2024-02-01", result2);
    }

    @Test
    void _09_invalid_pattern_handling() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD);
        Date testDate = sdf.parse(DATE_STR_YYYYMMDD_TEST);

        // 使用无效的格式模式
        assertThatThrownBy(() -> DateUtils.parseDateToStr("invalid-pattern", testDate)).isInstanceOf(
                IllegalArgumentException.class);
    }

    @ParameterizedTest
    @MethodSource("provideDatesForBoundaryTests")
    void _10_boundary_dates_juLian_conversion(Date input, String expected) {
        String result = DateUtils.convertDateToJuLian(input);
        assertEquals(expected, result);
    }

    @ParameterizedTest
    @ValueSource(strings = {"124001", "124031", "124060", "124366"})
    void _11_boundary_dates_convertToYYYYMMDD(String julianDate) {
        String result = DateUtils.convertDateYYYYMMDD(julianDate);
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2}"));
    }

    // 提供参数化测试的数据源
    private static Stream<Arguments> provideDatesForBoundaryTests() {
        return Stream.of(
                // 测试边界日期 - 月初
                Arguments.of(toDate(2024, 1, 1), "124001"),
                // 测试边界日期 - 月末
                Arguments.of(toDate(2024, 1, 31), "124031"),
                // 测试边界日期 - 年初
                Arguments.of(toDate(2024, 1, 1), "124001"),
                // 测试边界日期 - 年末
                Arguments.of(toDate(2024, 12, 31), "124366"),
                // 测试闰年2月29日
                Arguments.of(toDate(2024, 2, 29), "124060"));
    }

    // 辅助方法，将年月日转换为Date对象
    private static Date toDate(int year, int month, int day) {
        return Date.from(LocalDate.of(year, month, day)
                                  .atStartOfDay(ZoneId.systemDefault())
                                  .toInstant());
    }

}
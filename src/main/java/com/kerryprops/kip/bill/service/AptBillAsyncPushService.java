package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillNotifyChannel;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.webservice.vo.req.BillNotifyRequest;

import java.util.List;

public interface AptBillAsyncPushService {

    /**
     * 异步推送账单方法.
     *
     * @param aptBillList     需要推送的账单列表.
     * @param aptBillOperator 账单操作类型，表示此次操作的具体类型.
     * @param conversationId  会话ID，用于标识此次操作的请求链路.
     * @param userInfo        当前登录用户的信息，用于操作日志及权限校验.
     */
    void asyncPushAptBill(List<AptBill> aptBillList, AptBillOperator aptBillOperator, String conversationId,
                          LoginUser userInfo);

    /**
     * 通过指定通知渠道异步推送未支付账单.
     *
     * @param notifyChannels 通知渠道列表，指定需要推送账单的渠道，如微信模板消息、短信等.
     * @param aptBills       账单列表，需要推送的未支付账单信息.
     * @param conversationId 会话ID，用于标识此次操作的唯一请求链路.
     * @param userInfo       当前登录用户的信息，用于权限校验及记录操作日志.
     */
    void pushUnpaidBill(List<BillNotifyChannel> notifyChannels, List<AptBill> aptBills, String conversationId,
                        LoginUser userInfo);

    /**
     * 异步推送未支付账单至指定用户的方法.
     *
     * @param notifyChannels 通知渠道列表，指定需要推送账单的渠道，如微信模板消息、短信等.
     * @param aptBills       账单列表，包含需要推送的未支付账单信息.
     * @param selectUsers    选定的用户列表，指定推送账单的目标用户.
     * @param conversationId 会话ID，用于标识此次操作的唯一请求链路.
     * @param userInfo       当前登录用户的信息，用于权限校验及记录操作日志.
     */
    void pushUnpaidBillForSelectUsers(List<BillNotifyChannel> notifyChannels, List<AptBill> aptBills,
                                      List<BillNotifyRequest.SelectUser> selectUsers, String conversationId,
                                      LoginUser userInfo);

}

package com.kerryprops.kip.bill.service.model.s;

import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.feign.entity.TenantStaffResponse;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * BillReceiver.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Yu 2025-06-11 16:27:38
 **/
@Getter
@Setter
public class BillReceiver {

    @Schema(title = "姓名")
    private String userName;

    @Schema(title = "用户id")
    private String userId;

    @Schema(title = "项目id")
    private String projectId;

    @Schema(title = "项目名称")
    private String projectName;

    @Schema(title = "楼栋id")
    private String buildingId;

    @Schema(title = "楼栋名称")
    private String buildingName;

    @Schema(title = "房间id")
    private String roomId;

    @Schema(title = "房间号")
    private String roomNo;

    @Schema(title = "电话号码")
    private String phoneNumber;

    @Schema(title = "授权标识", description = "1：授权用户 || 2:业主")
    private Integer authorizer;

    public static BillReceiver of(AptBill bill, TenantStaffResponse tenantStaff) {
        String projectName = bill.getPositionItem()
                                 .getProjectName();
        BillReceiver receiver = new BillReceiver();
        receiver.setUserName(tenantStaff.getUserName());
        receiver.setUserId(tenantStaff.getUserId());
        receiver.setProjectId(tenantStaff.getProjectId());
        receiver.setProjectName(projectName);
        receiver.setBuildingId(tenantStaff.getBuildingNumber());
        receiver.setBuildingName(StringUtils.substringBeforeLast(tenantStaff.getBuildingName(), "-"));
        receiver.setRoomId(bill.getRoomId());
        receiver.setRoomNo(bill.getPositionItem()
                               .getRoomName());
        receiver.setPhoneNumber(tenantStaff.getMobile());
        receiver.setAuthorizer(tenantStaff.getAuthorizer());
        return receiver;
    }

    public static BillReceiver of(RoomResp room, TenantStaffResponse tenantStaff) {
        var project = room.getProject();
        var building = room.getBuilding();
        var roomDto = room.getRoom();

        BillReceiver receiver = new BillReceiver();
        receiver.setUserName(tenantStaff.getUserName());
        receiver.setUserId(tenantStaff.getUserId());
        receiver.setPhoneNumber(tenantStaff.getMobile());
        receiver.setAuthorizer(tenantStaff.getAuthorizer());
        receiver.setProjectId(project.getId());
        receiver.setProjectName(project.getName());
        receiver.setBuildingId(building.getId());
        receiver.setBuildingName(building.getName());
        receiver.setRoomId(roomDto.getId());
        receiver.setRoomNo(roomDto.getRoomNo());
        return receiver;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BillReceiver that = (BillReceiver) o;
        return Objects.equals(userId, that.userId) && Objects.equals(roomId, that.roomId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, roomId);
    }

}

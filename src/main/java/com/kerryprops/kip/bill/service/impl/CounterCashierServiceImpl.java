package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.enums.AptPayVerifyStatus;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.CashierPayStatus;
import com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum;
import com.kerryprops.kip.bill.common.enums.PaymentCateEnum;
import com.kerryprops.kip.bill.common.exceptions.CounterCashierBizException;
import com.kerryprops.kip.bill.common.jpa.entity.BaseEntity;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.BillingFun.ValidationSupplier;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptPayBillRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.AptPaymentBillRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayBill;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentBill;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.HiveContextAware;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.dao.entity.QAptPaymentInfo;
import com.kerryprops.kip.bill.service.CounterCashierService;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.model.s.AptPayInvoiceBo;
import com.kerryprops.kip.bill.service.model.s.BillInvoiceCalcDto;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillPaymentRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierAptPaySearchRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierOfflinePayRequest;
import com.kerryprops.kip.bill.webservice.vo.req.CashierQRCodePaymentRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPayExportVo;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierAptPaymentInfoResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierOfflinePayResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierPaymentReceiptResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierPaymentTransactionResource;
import com.kerryprops.kip.bill.webservice.vo.resp.CashierQRCodePaymentResource;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.bill.webservice.vo.resp.RoomAn8RespVo;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import com.kerryprops.kip.pmw.client.resource.HeaderResource;
import com.kerryprops.kip.pmw.client.resource.LoopQueryPspOutputResource;
import com.kerryprops.kip.pmw.client.resource.LoopQueryPspOutputResource.LoopQueryPspOutputBodyResource;
import com.kerryprops.kip.pmw.client.resource.QueryOrderInputResource;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.DEFAULT_ZONE_ID;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exportByEasyExcel;

/**
 * <AUTHOR> 2023-11-17 16:25:34
 **/
@Slf4j
@Service
@AllArgsConstructor
public class CounterCashierServiceImpl extends AbstractCounterCashierService implements CounterCashierService {

    private final AptBillService aptBillService;

    private final AptPayRepository aptPayRepository;

    private final AptBillRepository aptBillRepository;

    private final PaymentBillService paymentBillService;

    private final AptPaymentBillRepository aptPaymentBillRepository;

    private final AptPayBillRepository payBillRepository;

    @Override
    @Transactional
    public CashierOfflinePayResource offlinePay(CashierOfflinePayRequest request) {
        validAdvanceAmount(request);
        AptPayConfig payConfig = getPayConfig(request);
        List<AptBill> bills = getBills(request);
        AptBill bill = bills.isEmpty() ? null : bills.get(0);
        PositionItemResponse pos = Objects.nonNull(bill) ? bill.getPositionItem() : getPositionByRoomId(request.getRoomId());
        Optional<RoomAn8RespVo> roomAn8 = aptBillRepository.findLastRoomAn8(request.getRoomId());

        AptPaymentInfo qo = request.toAptPaymentInfo(pos);
        roomAn8.ifPresent(v -> v.fillRoomAn8(qo));
        aptPaymentInfoRepository.insert(qo);

        AptPay aptPay = request.toAptPay(bill, qo, payConfig, pos);
        roomAn8.ifPresent(v -> v.fillRoomAn8(aptPay));
        aptPayRepository.insert(aptPay);

        String paymentId = qo.getId();
        Long payId = aptPay.getId();
        List<Long> billIds = request.getBillIds();
        if (CollectionUtils.isNotEmpty(bills)) {
            for (AptBill aptBill : bills) {
                Long billId = aptBill.getId();
                AptPaymentBill paymentBill = new AptPaymentBill();
                paymentBill.setPaymentInfoId(paymentId);
                paymentBill.setBillId(billId);
                paymentBill.setDeleted(0);
                aptPaymentBillRepository.insert(paymentBill);

                AptPayBill payBill = new AptPayBill();
                payBill.setPayId(payId);
                payBill.setBillId(billId);
                payBill.setBillNo(aptBill.getBillNo());
                payBillRepository.insert(payBill);
            }
            aptBillRepository.updateBillStatus4cashier(billIds);
        }

        CashierOfflinePayResource resource = new CashierOfflinePayResource();
        resource.setPayAct(aptPay.getPayAct());
        resource.setPaymentInfoId(paymentId);
        return resource;
    }

    @Override
    @Transactional
    public CashierQRCodePaymentResource counterCashierQRCodePayment(CashierQRCodePaymentRequest request) {
        if (Objects.nonNull(request.getAdvanceAmount()) && request.getAdvanceAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 有预收的情况下，advance_amount, actual_amount, payment_cate不允许为空
            if (Objects.isNull(request.getPaymentCate()) || Objects.isNull(request.getActualAmount())
                    || Objects.isNull(request.getRoomId())) {
                throw new CounterCashierBizException("700014", "预收信息有误");
            }
            if (request.getAdvanceAmount().compareTo(request.getActualAmount()) == 0
                    && CollectionUtils.isEmpty(request.getBills())) {
                return new PurePreCollectionQRCodePaymentHandler(request).handle();
            }
            return new AptBillWithPreCollectionQRCodePaymentHandler(request).handle();
        }
        return new CounterCashierPaymentHandler(request.getPayOption(), request.getAuthCode()
                , request.getBills(), request.getPaymentDesc(), request.getPayerInfo()
                , request.getPaymentTime()).handle();
    }

    /**
     * 查询物业前台的支付交易单
     */
    @Override
    public CashierAptPaymentInfoResource queryCashierPaymentInfo(String paymentInfoId) {
        AptPaymentInfo aptPaymentInfo = paymentBillService.getBillPaymentInfoById(paymentInfoId);
        if (Objects.isNull(aptPaymentInfo)) {
            throw new CounterCashierBizException("400011", "物业缴费交易不存在");
        }
        CashierAptPaymentInfoResource resource = BeanUtil.copy(aptPaymentInfo, CashierAptPaymentInfoResource.class);
        if (!BillPaymentStatus.PAYING.equals(resource.getPaymentStatus())) {
            return resource;
        }
        // validate TTL, set to CANCEL while expired
        if (resource.getCreateTime().plusSeconds(paymentConfigProps.getUserQRCodePaymentTtlSeconds())
                .compareTo(ZonedDateTime.now(DEFAULT_ZONE_ID)) <= 0) {
            // is expired
            resource.setCancelType(PayCancelTypeEnum.TIMEOUT_CANCELLED.name());
            resource.setPaymentStatus(BillPaymentStatus.CANCEL);
            // call payment API to cancel transaction
            paymentBillService.cancelPaymentTransaction(aptPaymentInfo);
        } else {
            QueryOrderInputResource queryOrderInputResource = getQueryOrderInputResource(aptPaymentInfo);
            LoopQueryPspOutputResource outputResource = paymentClientService.loopQueryPspOrder(queryOrderInputResource);
            LoopQueryPspOutputBodyResource loopQueryPspOutputBodyResource = outputResource.getBody();
            log.info("loop_query_payment_order result: {}", loopQueryPspOutputBodyResource.getState());
            if (StringUtils.isNotBlank(loopQueryPspOutputBodyResource.getPspDesc())) {
                resource.setFailedReason(loopQueryPspOutputBodyResource.getPspDesc());
            } else {
                resource.setFailedReason("支付中");
            }
            if ("FAILED".equalsIgnoreCase(loopQueryPspOutputBodyResource.getState())) {
                resource.setPaymentStatus(BillPaymentStatus.CANCEL);
            }
        }
        return resource;
    }

    @Override
    public Page<CashierPaymentTransactionResource> queryCashierAptPays(CashierAptPaySearchRequest searchRequest
            , Pageable pageable) {
        JPAQuery<Tuple> query = generateJPAQuery(searchRequest);
        long count = query.fetchCount();

        QAptPay aptPayQuery = QAptPay.aptPay;
        QAptPaymentInfo aptPaymentInfoQuery = QAptPaymentInfo.aptPaymentInfo;
        List<CashierPaymentTransactionResource> resources = query.offset(pageable.getOffset())
                .limit(pageable.getPageSize())
                .orderBy(aptPayQuery.createTime.desc(), aptPayQuery.updateTime.desc())
                .fetch()
                .stream()
                .map(v -> {
                    AptPay aptPay = v.get(aptPayQuery);
                    AptPaymentInfo paymentInfo = v.get(aptPaymentInfoQuery);
                    Objects.requireNonNull(aptPay);

                    CashierPaymentTransactionResource resource = CashierPaymentTransactionResource.of(aptPay);
                    CashierPayStatus cashierPayStatus = CashierPayStatus.of(paymentInfo, aptPay);
                    BillInvoiceCalcDto dto = BillInvoiceCalcDto.of(aptPay);
                    AptPayInvoiceBo bo = paymentBillService.calcBillInvoice(dto);

                    resource.setCanInvoiceBillAmount(bo.getCanInvoiceAmount());
                    resource.setIsAdvanceBilling(bo.getIsAdvanceBilling());
                    resource.setStatus(cashierPayStatus);
                    return resource;
                })
                .collect(Collectors.toList());
        return new PageImpl<>(resources, pageable, count);
    }

    @Override
    public void exportCashierAptPays(CashierAptPaySearchRequest searchRequest, HttpServletResponse response) {
        var aptPayQuery = QAptPay.aptPay;
        var exportVos = generateJPAQuery(searchRequest)
                .orderBy(aptPayQuery.createTime.desc(), aptPayQuery.updateTime.desc())
                .fetch().stream().map(cashierAptPayInfo -> {
                    AptPay aptPay = cashierAptPayInfo.get(aptPayQuery);
                    Objects.requireNonNull(aptPay);

                    CashierPaymentTransactionResource resource = CashierPaymentTransactionResource.of(aptPay);
                    AptPayExportVo exportVo = BeanUtil.copy(resource, AptPayExportVo.class);

                    exportVo.setProjectName(aptPay.getPositionItem().getProjectName());
                    exportVo.setBuildingName(aptPay.getPositionItem().getBuildingName());
                    exportVo.setRoomNo(aptPay.getPositionItem().getRoomName());
                    exportVo.setPayChannel(aptPay.getPayChannel().getInfo());
                    exportVo.setComments(resource.getComment());
                    exportVo.setAdvanceAmount(resource.getAdvanceAmt());
                    exportVo.setTaxAmt(aptPay.getTaxAmt());

                    return exportVo;
                }).collect(Collectors.toList());

        final String FILE_NAME = "cashier_apt_pays_file";
        final String SHEET_NAME = "cashier_apt_pays";
        exportByEasyExcel(response, exportVos, AptPayExportVo.class, FILE_NAME, SHEET_NAME);
    }

    @Override
    public CashierPaymentReceiptResource acquirePaymentReceipt(String paymentInfoId) {
        AptPaymentInfo paymentInfo = aptPaymentInfoRepository.findTopById(paymentInfoId);
        AptPay aptPay = aptPayRepository.findTopByPaymentInfoId(paymentInfoId);
        BillInvoiceCalcDto dto = BillInvoiceCalcDto.of(paymentInfo);
        AptPayInvoiceBo bo = paymentBillService.calcBillInvoice(dto);

        Map<Long, String> canBillInvoice = bo.getCanBillInvoice();
        List<CashierPaymentReceiptResource.Bill> bills = bo.getBills()
                .stream()
                .map(v -> {
                    CashierPaymentReceiptResource.Bill bill = CashierPaymentReceiptResource.Bill.of(v);
                    bill.setIsBilling(canBillInvoice.get(v.getId()));
                    return bill;
                })
                .collect(Collectors.toList());

        CashierPaymentReceiptResource resource = CashierPaymentReceiptResource.of(paymentInfo, aptPay);
        resource.setPayer(paymentInfo.getAlph());
        resource.setBills(bills);
        resource.setCanInvoiceBillAmount(bo.getCanInvoiceAmount());
        resource.setBillAmount(bo.getBillAmount());
        resource.setIsAdvanceBilling(bo.getIsAdvanceBilling());
        return resource;
    }

    private static QueryOrderInputResource getQueryOrderInputResource(AptPaymentInfo aptPaymentInfo) {
        QueryOrderInputResource.QueryOrderInputBodyResource body
                = new QueryOrderInputResource.QueryOrderInputBodyResource();
        body.setOrderNo(aptPaymentInfo.getId());
        body.setOrderSource("KIP_BILLING");
        HeaderResource.Builder builder = new HeaderResource.Builder();
        builder.setSub("kip");
        HeaderResource headerResource = builder.build();
        return new QueryOrderInputResource(headerResource, body, null);
    }

    private JPAQuery<Tuple> generateJPAQuery(CashierAptPaySearchRequest searchRequest) {
        CashierPayStatus status = searchRequest.getStatus();
        QAptPay aptPayQuery = QAptPay.aptPay;
        QAptPaymentInfo aptPaymentInfoQuery = QAptPaymentInfo.aptPaymentInfo;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(searchRequest.toPredicates());
        if (Objects.equals(status, CashierPayStatus.CANCELLED)) {
            builder.and(aptPayQuery.deletedAt.eq(1));
        }
        if (Objects.equals(status, CashierPayStatus.VERIFIED)) {
            builder.and(aptPayQuery.verifyStatus.eq(AptPayVerifyStatus.VERIFIED).and(aptPayQuery.deletedAt.eq(0)));
        }
        // 如果是C端产生的支付记录，不显示 超时或主动取消 的支付记录
        var excludeClientCancel = aptPaymentInfoQuery.billPayModule.eq(BillPayModule.KERRY)
                .and(aptPaymentInfoQuery.cancelType.isNull().or(aptPaymentInfoQuery.cancelType.notIn(PayCancelTypeEnum.TIMEOUT_CANCELLED.name(), PayCancelTypeEnum.USER_CANCELLED.name())));
        builder.and(aptPaymentInfoQuery.billPayModule.eq(BillPayModule.CASHIER).or(excludeClientCancel));

        JPAQueryFactory queryFactory = aptPaymentInfoRepository.getJpaQueryFactory();
        return queryFactory.select(aptPaymentInfoQuery, aptPayQuery)
                .from(aptPayQuery)
                .join(aptPaymentInfoQuery).on(aptPaymentInfoQuery.id.eq(aptPayQuery.paymentInfoId))
                .where(builder);
    }

    private PositionItemResponse getPositionByRoomId(String roomId) {
        RespWrapVo<RoomResp> roomRespVoRespWrapVo = hiveAsClient.getRoomById(roomId);
        if (!RespWrapVo.isResponseValidWithData(roomRespVoRespWrapVo)) {
            throw new RuntimeException("room not found: " + roomId);
        }
        RoomResp roomResp = roomRespVoRespWrapVo.getData();
        PositionItemResponse pos = new PositionItemResponse();
        pos.setProjectName(roomResp.getProject().getName());
        pos.setBuildingName(roomResp.getBuilding().getName());
        pos.setFloorName(roomResp.getFloor().getName());
        pos.setRoomName(roomResp.getRoom().getRoomNo());
        return pos;
    }

    private void validAdvanceAmount(CashierOfflinePayRequest request) {
        List<Long> billIds = request.getBillIds();
        if (CollectionUtils.isNotEmpty(billIds)) {
            List<AptBill> aptBills = aptBillRepository.findByIdIn(billIds);
            if (CollectionUtils.isEmpty(aptBills)) {
                throw new CounterCashierBizException("400013", "没有找到具体账单信息");
            }
            BigDecimal billAmount = BigDecimal.valueOf(0.00D);
            for (AptBill aptBill : aptBills) {
                double amt = aptBill.getAmt();
                billAmount = billAmount.add(BigDecimal.valueOf(amt));
            }
            BigDecimal actualAmount = request.getActualAmount();
            BigDecimal exceptAdvanceAmount = BillUtil.formatAmount(actualAmount.subtract(billAmount));

            BigDecimal advanceAmount = Optional.ofNullable(request.getAdvanceAmount())
                    .map(BillUtil::formatAmount).orElse(BigDecimal.ZERO);
            if (!Objects.equals(advanceAmount, exceptAdvanceAmount)) {
                throw new CounterCashierBizException("400014", "预收金额异常");
            }
        }
    }

    private List<AptBill> getBills(CashierOfflinePayRequest request) {
        List<Long> billIds = request.getBillIds();
        if (CollectionUtils.isEmpty(billIds)) {
            return Collections.emptyList();
        }
        return aptBillRepository.findByIdIn(billIds);
    }

    private AptPayConfig getPayConfig(CashierOfflinePayRequest request) {
        String projectId = request.getProjectId();
        AptPayConfig vo = aptPayConfigRepository.findTopByProjectIdAndPaymentType(projectId, request.getPayType());
        if (Objects.isNull(vo)) {
            throw new CounterCashierBizException("400012", "当前楼盘未配置收款方式，请与物业联系");
        }
        return vo;
    }

    private class CounterCashierPaymentHandler extends AbstractCounterCashierPaymentHandler {

        private final Collection<String> targetBillNos;

        private final List<AptBillPaymentRequest> targetBills;

        private List<AptBill> aptBills;

        public CounterCashierPaymentHandler(String payOption, String authCode
                , List<AptBillPaymentRequest> bills, String paymentDesc
                , RoomAn8RespVo payerInfo, Date paymentTime) {
            super(payOption, authCode, paymentDesc, payerInfo, paymentTime);
            this.targetBills = bills;
            this.targetBillNos = targetBills.stream().map(AptBillPaymentRequest::getBillNo)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        }

        /**
         * 计算支付金额
         */
        @Override
        BigDecimal getPaymentAmount() {
            return BillUtil.formatAmount(aptBills.stream().map(AptBill::getAmt).map(BigDecimal::valueOf)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
        }

        /**
         * 获取hive信息
         */
        @Override
        Supplier<HiveContextAware> fetchHiveContext() {
            return () -> aptBills.get(0);
        }

        /**
         * 执行校验
         */
        @Override
        ValidationSupplier cashierPaymentValidation() {
            ValidationSupplier v = this::validateAptBills;
            return v.andThen(this::validateBillAmount).andThen(super.cashierPaymentValidation());
        }

        /**
         * 入库前，按需对payment info信息做定制化处理。
         */
        @Override
        Consumer<AptPaymentInfo> beforePaymentInfoSaved() {
            return paymentInfo -> {
                paymentInfo.setBillPayModule(BillPayModule.CASHIER);
                Collection<Long> billIds = aptBills.stream().map(BaseEntity::getId).collect(Collectors.toSet());
                Collection<AptPaymentBill> billPayments = billIds.stream().map(billId -> {
                    AptPaymentBill paymentBill = new AptPaymentBill();
                    paymentBill.setPaymentInfoId(paymentInfo.getId());
                    paymentBill.setBillId(billId);
                    paymentBill.setDeleted(0);
                    return paymentBill;
                }).collect(Collectors.toSet());
                aptPaymentBillRepository.saveAll(billPayments);
            };
        }

        /**
         * 参考 {@link PaymentBillServiceImpl#billPayment(List, boolean)}，校验账单是否前后一致。
         */
        private boolean validateAptBills() {
            this.aptBills = aptBillService.queryAptBillsByBillNos(this.targetBillNos, BillPaymentStatus.TO_BE_PAID);
            if (CollectionUtils.isEmpty(aptBills) || aptBills.size() != this.targetBillNos.size()) {
                throw new CounterCashierBizException("700007", "部分账单不可支付，请核对后重试");
            }
            return true;
        }

        /**
         * 参考 {@link PaymentBillServiceImpl#billPayment(List, boolean)}，校验金额是否一致。
         * 比如收银员长时间开着网页，后端金额即使有变化而页面也不会自动刷新，此时页面发起支付，会出现金额不一致的情况。
         */
        private boolean validateBillAmount() {
            Map<String, List<AptBillPaymentRequest>> requestedBillMap = targetBills.stream()
                    .collect(Collectors.groupingBy(AptBillPaymentRequest::getBillNo));
            // 逐个比对账单的金额，不一致直接报错
            aptBills.forEach(aptBill -> {
                        String billNo = aptBill.getBillNo();
                        AptBillPaymentRequest reqBill = requestedBillMap.get(billNo).get(0);
                        BigDecimal billDbAmount = BigDecimal.valueOf(aptBill.getAmt());
                        if (billDbAmount.compareTo(reqBill.getAmount()) != 0) {
                            throw new CounterCashierBizException("700009", "账单金额有变化，请刷新");
                        }
                    }
            );
            return true;
        }

    }

    /**
     * 账单扫码缴费收款，有预收的情况，增加处理预收信息。
     */
    private class AptBillWithPreCollectionQRCodePaymentHandler extends CounterCashierPaymentHandler {

        private final BigDecimal actualAmount;

        private final BigDecimal advanceAmount;

        private final PaymentCateEnum paymentCate;

        AptBillWithPreCollectionQRCodePaymentHandler(CashierQRCodePaymentRequest request) {
            super(request.getPayOption(), request.getAuthCode(), request.getBills()
                    , request.getPaymentDesc(), request.getPayerInfo(), request.getPaymentTime());
            this.paymentCate = request.getPaymentCate();
            this.actualAmount = BillUtil.formatAmount(request.getActualAmount());
            this.advanceAmount = BillUtil.formatAmount(request.getAdvanceAmount());
        }

        @Override
        ValidationSupplier cashierPaymentValidation() {
            return super.cashierPaymentValidation().andThen(this::validateAdvanceAmount);
        }

        @Override
        BigDecimal getPaymentAmount() {
            return this.actualAmount;
        }

        @Override
        Consumer<AptPaymentInfo> beforePaymentInfoSaved() {
            return super.beforePaymentInfoSaved().andThen(paymentInfo -> {
                paymentInfo.setPaymentCate(this.paymentCate);
                paymentInfo.setAdvanceAmount(this.advanceAmount);
            });
        }

        private boolean validateAdvanceAmount() {
            if (this.actualAmount.compareTo(super.getPaymentAmount().add(this.advanceAmount)) != 0) {
                throw new CounterCashierBizException("700013", "实收金额不等于(预收金额+账单金额)");
            }
            return true;
        }

    }

    /**
     * 仅预收费用扫码收款，无勾选账单
     */
    private class PurePreCollectionQRCodePaymentHandler extends AbstractCounterCashierPaymentHandler {

        private final BigDecimal advanceAmount;

        private final PaymentCateEnum paymentCate;

        private final HiveContextAware hiveInfo;

        PurePreCollectionQRCodePaymentHandler(CashierQRCodePaymentRequest request) {
            super(request.getPayOption(), request.getAuthCode(), request.getPaymentDesc()
                    , request.getPayerInfo(), request.getPaymentTime());
            this.paymentCate = request.getPaymentCate();
            this.advanceAmount = BillUtil.formatAmount(request.getAdvanceAmount());
            this.hiveInfo = hiveAsClient.getRoomHiveContext(request.getRoomId());
        }

        @Override
        BigDecimal getPaymentAmount() {
            return this.advanceAmount;
        }

        @Override
        Supplier<HiveContextAware> fetchHiveContext() {
            return () -> this.hiveInfo;
        }

        @Override
        Consumer<AptPaymentInfo> beforePaymentInfoSaved() {
            return paymentInfo -> {
                paymentInfo.setBillPayModule(BillPayModule.CASHIER);
                paymentInfo.setAdvanceAmount(this.advanceAmount);
                paymentInfo.setPaymentCate(this.paymentCate);
            };
        }

    }

}

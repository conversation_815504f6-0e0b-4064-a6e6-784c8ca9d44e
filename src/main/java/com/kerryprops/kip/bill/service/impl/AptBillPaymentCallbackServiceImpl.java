package com.kerryprops.kip.bill.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.BillPayStateEnum;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.PayCancelTypeEnum;
import com.kerryprops.kip.bill.common.enums.PaymentPayType;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayConfigRepository;
import com.kerryprops.kip.bill.dao.AptPaymentBillRepository;
import com.kerryprops.kip.bill.dao.AptPaymentInfoRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptPayConfig;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.QAptJdeBill;
import com.kerryprops.kip.bill.dao.entity.QAptPayConfig;
import com.kerryprops.kip.bill.feign.entity.PaymentConfirmedCommand;
import com.kerryprops.kip.bill.service.AptBillDirectDebitsBatchBillService;
import com.kerryprops.kip.bill.service.AptBillPaymentCallbackService;
import com.kerryprops.kip.bill.service.AptBillWxTemplateMsgService;
import com.kerryprops.kip.bill.service.AptPayService;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.pmw.client.resource.AsynPaymentResultResource.AsynPaymentResultBodyResource;
import com.kerryprops.kip.pmw.client.resource.AsyncPaymentFailedResource;
import com.kerryprops.kip.pmw.client.resource.AsyncPaymentFailedResource.AsyncPaymentFailedBodyResource;
import com.kerryprops.kip.pmw.client.security.CipherUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.DEFAULT_ZONE_ID;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;

@Slf4j
@Service
@AllArgsConstructor
public class AptBillPaymentCallbackServiceImpl implements AptBillPaymentCallbackService {

    private final CipherUtil cipherUtil;

    private final DataMigrationConfig dm;

    private final ObjectMapper objectMapper;

    private final AptPayService aptPayService;

    private final AptBillRepository aptBillRepository;

    private final AptPaymentBillRepository aptPaymentBillRepository;

    private final AptJdeBillRepository jdeBillRepository;

    private final AptPayConfigRepository payConfigRepository;

    private final AptPaymentInfoRepository paymentInfoRepository;

    private final AptBillWxTemplateMsgService templateMsgService;

    private final AptBillDirectDebitsBatchBillService batchBillService;


    @Override
    @Transactional
    public void handlePaymentCallback(AsynPaymentResultBodyResource backResource) {
        getHandler(backResource).handle();
    }

    @Override
    @Transactional
    public void handlePaymentFailedCallback(AsyncPaymentFailedResource paymentFailedResource) {
        // 校验回调签名
        cipherUtil.verify(paymentFailedResource);

        AsyncPaymentFailedBodyResource bodyResource = paymentFailedResource.getBody();
        log.info("received_pay_failed_callback req: {}", exceptionToNull(() -> objectMapper.writeValueAsString(bodyResource)));
        String paymentInfoId = bodyResource.getOrderNo();
        String errorMsg = bodyResource.getErrorMsg();
        if (StringUtils.isBlank(paymentInfoId)) {
            log.info("received_pay_failed_callback, empty paymentInfoId");
            return;
        }
        if (StringUtils.isBlank(errorMsg)) {
            log.info("received_pay_failed_callback, empty errorMsg");
            return;
        }
        Optional<AptPaymentInfo> op = paymentInfoRepository.findById(paymentInfoId);
        if (op.isEmpty()) {
            log.info("received_pay_failed_callback, aptPaymentInfo not found");
            return;
        }
        AptPaymentInfo paymentInfo = op.get();
        String oldFailedReason = paymentInfo.getFailedReason();
        if (!Objects.equals(errorMsg, oldFailedReason) && !"success".equalsIgnoreCase(errorMsg)) {
            if ("扣款超时".equals(oldFailedReason)) {
                paymentInfo.setFailedReason(errorMsg);
            } else {
                paymentInfo.setFailedReason(oldFailedReason + "; " + errorMsg);
            }
            paymentInfoRepository.save(paymentInfo);
            log.info("received_pay_failed_callback: update failed reason from '{}' to '{}'", oldFailedReason, errorMsg);
        }
    }

    AptPaymentCallbackHandler getHandler(AsynPaymentResultBodyResource backResource) {
        String paymentInfoId = backResource.getOrderNo();
        var paymentInfoOp = paymentInfoRepository.findById(paymentInfoId);
        if (paymentInfoOp.isEmpty()) {
            log.info("backResource OrderNo: {}", paymentInfoId);
            throw new AppException("700009", "PaymentInfo not found: " + paymentInfoId);
        }
        AptPaymentInfo paymentInfo = paymentInfoOp.get();
        BillPayModule billPayModule = paymentInfo.getBillPayModule();
        if (billPayModule == BillPayModule.CASHIER_FEE) {
            return new CashierFeeAptPaymentCallbackHandler(backResource, paymentInfo);
        } else if (Objects.nonNull(paymentInfo.getAdvanceAmount())
                && paymentInfo.getAdvanceAmount().compareTo(BigDecimal.ZERO) > 0
                && paymentInfo.getAdvanceAmount().compareTo(BigDecimal.valueOf(paymentInfo.getAmt())) == 0) {
            var coll = aptPaymentBillRepository.findAllByPaymentInfoIdAndDeleted(paymentInfoId, 0);
            if (CollectionUtils.isEmpty(coll)) {
                // 纯预收的情况使用如下处理，否则，使用common处理流程。
                return new CounterCashierPurePreCollectionPaymentCallbackHandler(backResource, paymentInfo);
            }
        }
        if (BillPaymentStatus.DIRECT_DEBIT_STATUSES.contains(paymentInfo.getPaymentStatus())) {
            return new DirectDebitsAptPaymentCallbackHandler(backResource, paymentInfo);
        }
        return new CommonAptPaymentCallbackHandler(backResource, paymentInfo);
    }

    /**
     * 处理普通回调
     */
    private class CommonAptPaymentCallbackHandler extends AptPaymentCallbackHandler {

        CommonAptPaymentCallbackHandler(AsynPaymentResultBodyResource backResource, AptPaymentInfo paymentInfo) {
            super(backResource, paymentInfo);
        }

        @Override
        void determineAptBills() {
            log.info("普通支付回调处理，开始查询账单信息");
            this.aptBills = aptBillRepository.queryAptBillByPaymentId(aptPaymentInfo.getId());
        }

        @Override
        void determineBillPayType2() {
            log.info("普通支付回调处理，开始逻辑确认支付类型");
            super.determineBillPayType2();
            if (PaymentPayType.ALI_WITHHOLD.equals(this.payType)) {
                this.payType = PaymentPayType.ALIPAY;
            } else if (PaymentPayType.WX_DIRECT_DEBIT.equals(this.payType)) {
                this.payType = PaymentPayType.WECHAT;
            }
            log.info("普通支付回调处理，支付类型最终逻辑确认为: {}", this.payType);
        }

        @Override
        void handleSuccess() {
            log.info("普通支付回调处理，开始处理成功状态");
            generalHandleSuccess(BillPaymentStatus.PAID);

            super.sendPaymentResultTemplateMsg(command -> {
                command.setHeadline("您好，您有一个物业账单支付成功");
                // 旧版本这个字段不显示了，新版本新版本复用此字段显示“房号”
                PositionItemResponse positionItem = aptPaymentInfo.getPositionItem();
                if (Optional.ofNullable(positionItem).map(PositionItemResponse::getRoomName).isPresent()) {
                    command.setRemark(positionItem.getProjectName() + "-" + positionItem.getBuildingName()
                            + "-" + positionItem.getRoomName());
                } else {
                    command.setRemark("点击详情 --> 去开票");
                }
                command.setUrl(dm.getWxPaymentConfirmedMsgUrl().replace("ORDER_NO", aptPaymentInfo.getId()));
            });
            log.info("普通支付回调处理，成功状态，微信模板消息发送完成");
        }

        @Override
        void handleExpired() {
            log.info("普通支付回调处理，开始处理过期状态");
            generalHandleExpired(BillPaymentStatus.CANCEL, BillPaymentStatus.PAYING);
            aptPayService.cancelAptPay(aptPaymentInfo, aptBills);
            log.info("普通支付回调处理，处理过期状态完成");
        }

    }

    /**
     * 处理代扣回调
     */
    private class DirectDebitsAptPaymentCallbackHandler extends AptPaymentCallbackHandler {

        DirectDebitsAptPaymentCallbackHandler(AsynPaymentResultBodyResource backResource, AptPaymentInfo paymentInfo) {
            super(backResource, paymentInfo);
        }

        @Override
        void determineAptBills() {
            log.info("代扣-支付回调处理，读取账单信息开始");
            aptBills = batchBillService.getAptBillsByPaymentOrderNo(paymentInfoId);
        }

        @Override
        boolean validatePaymentStatus() {
            log.info("代扣-支付回调处理，验证支付状态");
            if (!BillPaymentStatus.DIRECT_DEBIT_PAYING.equals(aptPaymentInfo.getPaymentStatus())) {
                log.warn("代扣-支付回调处理，支付状态已更改，不是待支付或支付中");
                return false;
            }
            log.info("代扣-支付回调处理，支付状态正常");
            return true;
        }

        @Override
        void handleSuccess() {
            log.info("代扣-支付回调处理，开始处理成功状态");
            generalHandleSuccess(BillPaymentStatus.DIRECT_DEBIT_PAID);

            super.sendPaymentResultTemplateMsg(command -> {
                if (PaymentPayType.ALIPAY.equals(paymentPayTypeEnum)) {
                    command.setHeadline("物业账单支付宝代扣成功");
                    command.setPaymentMethod("支付宝代扣");
                } else {
                    command.setHeadline("物业账单微信代扣成功");
                    command.setPaymentMethod("微信代扣");
                }
                PositionItemResponse positionItem = aptPaymentInfo.getPositionItem();
                if (Optional.ofNullable(positionItem).map(PositionItemResponse::getRoomName).isPresent()) {
                    command.setRemark(positionItem.getProjectName() + "-" + positionItem.getBuildingName()
                            + "-" + positionItem.getRoomName());
                } else {
                    command.setRemark("点击详情 --> 去开票");
                }
                command.setUrl(dm.getWxPaymentConfirmedMsgUrl().replace("ORDER_NO", aptPaymentInfo.getId()));
            });
            log.info("代扣-支付回调处理，成功状态，微信模板消息发送完成");
        }

        @Override
        void handleExpired() {
            log.info("代扣-支付回调处理，开始处理过期状态");
            if (StringUtils.isBlank(aptPaymentInfo.getFailedReason())) {
                aptPaymentInfo.setFailedReason("扣款超时");
            }
            generalHandleExpired(BillPaymentStatus.DIRECT_DEBIT_FAILED, BillPaymentStatus.DIRECT_DEBIT_PAYING);
            log.info("代扣-支付回调处理，处理过期状态完成");

            templateMsgService.sendDirectPaymentFailedMsg(aptPaymentInfo, callbackResource);
            log.info("代扣-支付回调处理，过期状态，微信模板消息发送完成");
        }

    }

    private class CashierFeeAptPaymentCallbackHandler extends AptPaymentCallbackHandler {

        public CashierFeeAptPaymentCallbackHandler(AsynPaymentResultBodyResource backResource
                , AptPaymentInfo paymentInfo) {
            super(backResource, paymentInfo);
        }

        @Override
        void determineAptBills() {
            this.aptBills = Collections.emptyList();
            log.info("收银台杂费支付回调处理，账单信息设置为空");
        }

        @Override
        void handleSuccess() {
            log.info("收银台杂费支付回调处理，开始处理成功状态");
            generalHandleSuccess(BillPaymentStatus.PAID);
        }

        @Override
        void handleExpired() {
            log.info("收银台杂费支付回调处理，开始处理过期状态");
            generalHandleExpired(BillPaymentStatus.CANCEL, BillPaymentStatus.PAYING);
            aptPayService.cancelAptPay(aptPaymentInfo, aptBills);
        }

    }

    private class CounterCashierPurePreCollectionPaymentCallbackHandler extends AptPaymentCallbackHandler {

        public CounterCashierPurePreCollectionPaymentCallbackHandler(AsynPaymentResultBodyResource backResource
                , AptPaymentInfo paymentInfo) {
            super(backResource, paymentInfo);
        }

        @Override
        void determineAptBills() {
            this.aptBills = Collections.emptyList();
            log.info("收银台杂费-纯预收-支付回调处理，账单信息设置为空");
        }

        @Override
        void handleSuccess() {
            log.info("收银台杂费-纯预收-支付回调处理，开始处理成功状态");
            generalHandleSuccess(BillPaymentStatus.PAID);
        }

        @Override
        void handleExpired() {
            log.info("收银台杂费-纯预收-支付回调处理，开始处理过期状态");
            generalHandleExpired(BillPaymentStatus.CANCEL, BillPaymentStatus.PAYING);
        }

    }

    /**
     * 回调处理类
     */
    private abstract class AptPaymentCallbackHandler {

        AsynPaymentResultBodyResource callbackResource;

        String paymentInfoId;

        AptPaymentInfo aptPaymentInfo;

        List<AptBill> aptBills;

        // cash | wechatpay | alipay
        PaymentPayType paymentPayTypeEnum;

        AptPayConfig payConfig;

        PaymentPayType payType;

        AptPaymentCallbackHandler(AsynPaymentResultBodyResource backResource, AptPaymentInfo aptPaymentInfo) {
            this.callbackResource = backResource;
            this.paymentInfoId = backResource.getOrderNo();
            this.aptPaymentInfo = aptPaymentInfo;
        }

        public void handle() {
            log.info("回调处理开始，支付信息ID: {}", paymentInfoId);

            determineAptBills();
            determinePayType();
            determineBillPayType2();

            String callbackState = callbackResource.getState();
            log.info("回调状态: {}", callbackState);

            if (BillPayStateEnum.SUCCESS.name().equalsIgnoreCase(callbackState)) {
                handleSuccess();
            } else if (BillPayStateEnum.EXPIRED.name().equalsIgnoreCase(callbackState)) {
                handleExpired();
            } else {
                handleOtherStatus();
            }
            log.info("回调处理完成，支付信息ID: {}", paymentInfoId);
        }

        void determinePayType() {
            log.info("开始读取支付类型");
            if (Objects.isNull(aptPaymentInfo.getPayType()) || PaymentPayType.UNKNOWN.equals(aptPaymentInfo.getPayType())) {
                this.paymentPayTypeEnum = PaymentPayType.fromPspName(callbackResource.getPspName());
            } else {
                this.paymentPayTypeEnum = aptPaymentInfo.getPayType();
            }
            log.info("支付类型读取为: {}", paymentPayTypeEnum);

        }

        void determineBillPayType2() {
            log.info("开始逻辑确认支付类型");
            payType = PaymentPayType.convertToBillPayType(paymentPayTypeEnum
                    , callbackResource.getPayOption());
            log.info("支付类型逻辑确认为: {}", payType);
        }

        void determinePayConfig() {
            log.info("开始查询支付配置");
            Iterable<AptPayConfig> payConfigIterable = payConfigRepository
                    .findAll(QAptPayConfig.aptPayConfig.paymentType.eq(payType.getInfo())
                            .and(QAptPayConfig.aptPayConfig.projectId.eq(aptPaymentInfo.getProjectId())));
            //2021-11-03, samatha - 不分区线上线下配置，如果有多个配置，则取第一个。
            if (payConfigIterable.iterator().hasNext()) {
                this.payConfig = payConfigIterable.iterator().next();
            } else {
                throw new AppException("7000010", "pay config not found");
            }
            log.info("支付配置查询成功");
        }

        void verifyJdeBills() {
            log.info("开始校验JDE账单");

            //verify jde bill.
            var billNos = aptBills.stream().map(AptBill::getBillNo).toList();
            Iterable<AptJdeBill> jdeBillIterable = jdeBillRepository.
                    findAll(QAptJdeBill.aptJdeBill.billNumber.in(billNos));
            jdeBillIterable.forEach(jdeBill -> {
                jdeBill.setOnlineVerification(1);
                jdeBill.setOnlineVerificationTime(new Date());
            });
            if (jdeBillIterable.iterator().hasNext()) {
                jdeBillRepository.saveAll(jdeBillIterable);
                log.info("JDE账单校验正常并保存成功: {}"
                        , exceptionToNull(() -> objectMapper.writeValueAsString(Lists.newLinkedList(jdeBillIterable))));
            }
        }

        void generalHandleSuccess(BillPaymentStatus paymentStatus) {
            log.info("开始处理成功状态，支付状态: {}", paymentStatus);

            determinePayConfig();
            aptPaymentInfo.setPaymentStatus(paymentStatus);
            aptPaymentInfo.setAppliedInvoice(1);
            // pay type bases on payment service provider name
            aptPaymentInfo.setPayType(paymentPayTypeEnum);
            String payTypeInfo = aptPaymentInfo.getPayTypeInfo();
            if (StringUtils.isBlank(payTypeInfo)) {
                aptPaymentInfo.setPayTypeInfo(paymentPayTypeEnum.getInfo());
            }
            // set pay time
            Date payTime;
            if (Objects.isNull(aptPaymentInfo.getPaymentTime())) {
                ZonedDateTime payDateTime = ZonedDateTime.parse(callbackResource.getFinishedDate()
                        , DateTimeFormatter.ISO_OFFSET_DATE_TIME.withZone(DEFAULT_ZONE_ID));
                payTime = Date.from(payDateTime.toInstant());
                aptPaymentInfo.setPaymentTime(payTime);
            } else {
                payTime = aptPaymentInfo.getPaymentTime();
            }
            aptPaymentInfo.setPaymentTransNo(callbackResource.getUniquePaymentId());
            aptPaymentInfo.setPspTransNo(callbackResource.getPspTin());
            // save payment info
            paymentInfoRepository.save(aptPaymentInfo);
            log.info("支付信息保存成功: {}", exceptionToNull(() -> objectMapper.writeValueAsString(aptPaymentInfo)));

            double paidAmt = BillUtil.formatAmount(new BigDecimal(callbackResource.getOrderAmount())).doubleValue();
            if (CollectionUtils.isEmpty(aptBills)) {
                aptPayService.savePayInfo(aptPaymentInfo, payConfig, callbackResource.getPspTin()
                        , paidAmt, payTime);
            } else {
                aptPayService.savePayInfo(aptPaymentInfo, payConfig, callbackResource.getPspTin()
                        , paidAmt, payTime, aptBills);
                for (AptBill aptBill : aptBills) {
                    aptBill.setPayTime(new Date());
                    if (BillPayModule.CASHIER.equals(aptPaymentInfo.getBillPayModule())) {
                        aptBill.setStatus(BillStatus.CASHIER_PAID);
                        aptBill.setPaymentResult(BillStatus.CASHIER_PAID.getInfo());
                    } else {
                        aptBill.setStatus(BillStatus.ONLINE_PAID);
                        if (BillPaymentStatus.DIRECT_DEBIT_PAID.equals(paymentStatus)) {
                            aptBill.setPaymentResult("代扣");
                        } else {
                            aptBill.setPaymentResult("线上支付");
                        }
                    }
                    aptBill.setPaymentStatus(paymentStatus);
                    aptBill.setUpdateTime(new Date());
                }
                aptBillRepository.saveAll(aptBills);
                for (AptBill bill : aptBills) {
                    log.info("账单支付信息更新成功: {}", exceptionToNull(() -> objectMapper.writeValueAsString(bill)));
                }
                // 更新apt_sync_bills 标记位
                verifyJdeBills();
            }
        }

        void generalHandleExpired(BillPaymentStatus paymentInfoStatus, BillPaymentStatus billPaymentStatus) {
            log.info("开始处理过期状态，支付信息状态: {}, 账单支付状态: {}", paymentInfoStatus, billPaymentStatus);

            if (Boolean.FALSE.equals(validatePaymentStatus())) {
                log.warn("支付状态验证失败，跳过处理");
                return;
            }

            aptPaymentInfo.setCancelType(PayCancelTypeEnum.TIMEOUT_CANCELLED.name());
            aptPaymentInfo.setPaymentStatus(paymentInfoStatus);
            aptPaymentInfo.setPayType(paymentPayTypeEnum);
            String payTypeInfo = aptPaymentInfo.getPayTypeInfo();
            if (StringUtils.isBlank(payTypeInfo)) {
                aptPaymentInfo.setPayTypeInfo(paymentPayTypeEnum.getInfo());
            }

            paymentInfoRepository.save(aptPaymentInfo);
            log.info("支付信息(aptPaymentInfo)更新成功");

            for (AptBill bill : aptBills) {
                // 还原apt_bill支付状态为：TO_BE_PAID
                if (billPaymentStatus.equals(bill.getPaymentStatus())) {
                    bill.setUpdateTime(new Date());
                    bill.setPaymentStatus(BillPaymentStatus.TO_BE_PAID);
                    aptBillRepository.save(bill);
                }
            }
            log.info("支付过期，账单状态更新为待支付完成");
        }

        void sendPaymentResultTemplateMsg(Consumer<PaymentConfirmedCommand> commandConsumer) {
            templateMsgService.sendPaymentResultNotice(this.aptPaymentInfo, this.callbackResource, commandConsumer);
        }

        abstract void determineAptBills();

        abstract void handleSuccess();

        abstract void handleExpired();

        boolean validatePaymentStatus() {
            log.info("验证支付状态");

            if (!List.of(BillPaymentStatus.TO_BE_PAID, BillPaymentStatus.PAYING)
                    .contains(aptPaymentInfo.getPaymentStatus())) {
                log.warn("支付状态已更改，不是待支付或支付中");
                return false;
            }
            log.info("支付状态正常");
            return true;
        }

        void handleOtherStatus() {
            throw new RuntimeException("un-supported PaymentInfo status.");
        }

    }

}
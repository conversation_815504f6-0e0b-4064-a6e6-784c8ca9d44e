package com.kerryprops.kip.bill.service;

import com.kerryprops.kip.bill.common.enums.BillNotifyChannel;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.service.model.s.BillReceiver;
import com.kerryprops.kip.bill.webservice.vo.req.BillNotifyRequest;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface AptBillPushService {

    /**
     * 推送公寓账单的接口方法.
     *
     * @param billMap         包含公寓账单的映射集合，key为房间ID的字符串，value为账单对象列表.
     * @param roomIds         需要推送账单的房间ID集合.
     * @param aptBillOperator 用于标识账单操作类型的操作者枚举.
     * @param projectId       项目标识ID，用来关联账单所属的项目.
     */
    void pushAptBill(Map<String, List<AptBill>> billMap, Set<String> roomIds, AptBillOperator aptBillOperator,
                     String projectId);

    /**
     * 根据提供的公寓账单列表，查找并返回对应的账单接收人集合.
     *
     * @param bills 包含公寓账单的列表，用于筛选和匹配账单接收人信息.
     * @return 根据传入账单生成的账单接收人集合.
     */
    Set<BillReceiver> findBillReceivers(List<AptBill> bills);

    /**
     * 推送未支付账单的接口方法.
     *
     * @param notifyChannel   通知渠道列表，用于指定账单推送的方式，例如微信、短信等.
     * @param aptBills        公寓账单列表，需要推送的账单信息集合.
     * @param aptBillOperator 账单操作类型的操作者枚举，用于标识和记录此次推送的操作.
     * @param projectId       项目标识ID，用于关联账单所属的具体项目.
     */
    void pushUnpaidBill(List<BillNotifyChannel> notifyChannel, List<AptBill> aptBills, AptBillOperator aptBillOperator,
                        String projectId);

    /**
     * 推送未支付账单给指定用户.
     *
     * @param notifyChannels  通知渠道列表，用于指定账单推送的方式，例如微信、短信等.
     * @param aptBills        公寓账单列表，需要推送的账单信息集合.
     * @param selectUsers     指定的用户ID列表，仅推送给这些用户.
     * @param aptBillOperator 账单操作类型的操作者枚举，用于标识和记录此次推送的操作.
     * @param projectId       项目标识ID，用于关联账单所属的具体项目.
     */
    void pushUnpaidBillForSelectUsers(List<BillNotifyChannel> notifyChannels, List<AptBill> aptBills,
                                      List<BillNotifyRequest.SelectUser> selectUsers, AptBillOperator aptBillOperator,
                                      String projectId);

}

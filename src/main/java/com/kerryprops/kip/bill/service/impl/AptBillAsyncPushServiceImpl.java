package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.enums.BillNotifyChannel;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAsyncPushService;
import com.kerryprops.kip.bill.service.AptBillPushService;
import com.kerryprops.kip.bill.webservice.vo.req.BillNotifyRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.log4j.BSConversationFilter.setLoggingContext;

@Slf4j
@Service
@RequiredArgsConstructor
public class AptBillAsyncPushServiceImpl implements AptBillAsyncPushService {

    private final AptBillPushService aptBillPushService;

    @Async
    @Override
    public void asyncPushAptBill(List<AptBill> aptBillList, AptBillOperator aptBillOperator, String conversationId,
                                 LoginUser userInfo) {
        setLoggingContext(conversationId, null);

        log.info("异步推送账单开始，账单数量: {}", aptBillList.size());
        UserInfoUtils.setUser(userInfo);

        Map<String, List<AptBill>> billMap = aptBillList.stream()
                                                        .filter(e -> StringUtils.isNotEmpty(e.getRoomId()))
                                                        .collect(Collectors.groupingBy(AptBill::getRoomId));
        Set<String> roomIds = billMap.keySet();
        log.info("账单按roomId分组完成");

        aptBillPushService.pushAptBill(billMap, roomIds, aptBillOperator, aptBillList.get(0)
                                                                                     .getProjectId());
        log.info("异步推送账单开始完成");
    }

    @Async
    @Override
    public void pushUnpaidBill(List<BillNotifyChannel> notifyChannels, List<AptBill> aptBills, String conversationId,
                               LoginUser userInfo) {
        setLoggingContext(conversationId, null);
        UserInfoUtils.setUser(userInfo);
        String projectId = aptBills.stream()
                                   .findFirst()
                                   .map(AptBill::getProjectId)
                                   .orElse(null);
        aptBillPushService.pushUnpaidBill(notifyChannels, aptBills, AptBillOperator.PUSH_CONDITIONAL, projectId);
    }

    @Async
    @Override
    public void pushUnpaidBillForSelectUsers(List<BillNotifyChannel> notifyChannels, List<AptBill> aptBills,
                                             List<BillNotifyRequest.SelectUser> selectUsers, String conversationId,
                                             LoginUser userInfo) {
        setLoggingContext(conversationId, null);
        UserInfoUtils.setUser(userInfo);
        String projectId = aptBills.stream()
                                   .findFirst()
                                   .map(AptBill::getProjectId)
                                   .orElse(null);
        aptBillPushService.pushUnpaidBillForSelectUsers(notifyChannels, aptBills, selectUsers,
                                                        AptBillOperator.PUSH_CONDITIONAL, projectId);
    }

}
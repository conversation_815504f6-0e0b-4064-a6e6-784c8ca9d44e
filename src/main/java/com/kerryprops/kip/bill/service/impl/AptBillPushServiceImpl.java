package com.kerryprops.kip.bill.service.impl;

import com.kerryprops.kip.bill.common.aop.RedisLock;
import com.kerryprops.kip.bill.common.current.JsonUtils;
import com.kerryprops.kip.bill.common.enums.BillNotifyChannel;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.utils.DiffFieldUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.config.properties.BillPushProperties;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.feign.clients.BUserClient;
import com.kerryprops.kip.bill.feign.clients.CUserClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.MessageCenterClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.MessageDto;
import com.kerryprops.kip.bill.feign.entity.MessageType;
import com.kerryprops.kip.bill.feign.entity.SendSmsV2Command;
import com.kerryprops.kip.bill.feign.entity.TemplateMsgType;
import com.kerryprops.kip.bill.feign.entity.TenantStaffResponse;
import com.kerryprops.kip.bill.feign.entity.WxOpenIDResource;
import com.kerryprops.kip.bill.feign.entity.WxSubscribeSendRequest;
import com.kerryprops.kip.bill.feign.entity.WxTemplateMsgRequestCommand;
import com.kerryprops.kip.bill.service.AptBillOperationService;
import com.kerryprops.kip.bill.service.AptBillPushService;
import com.kerryprops.kip.bill.service.model.s.BillReceiver;
import com.kerryprops.kip.bill.webservice.vo.req.BillNotifyRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.OperationChangedFiledRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.hiveas.webservice.resource.resp.RoomResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.APT_BILL_PUSH_REDIS_KEY_PREFIX;
import static com.kerryprops.kip.bill.common.constants.AppConstants.COMMA;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.refreshCorrelationId;

@Slf4j
@Service
@RequiredArgsConstructor
public class AptBillPushServiceImpl implements AptBillPushService {

    private static final List<BillPaymentStatus> HAS_PAID_STATUS =
            List.of(BillPaymentStatus.PAID, BillPaymentStatus.DIRECT_DEBIT_PAID);

    private static final String BILL_DATA_PATTERN = "yyyy年MM月";

    /**
     * 授权用户.
     */
    private static final int AUTH_USER = 1;

    /**
     * 业主.
     */
    private static final int OWNER = 2;

    private static final int MAX_MSG_LEN = 250;

    private static final String PUSH_BILL_FAIL_TIPS = "请联系系统管理员";

    private static final String INTERNAL_MSG_ERROR_TIPS_PREFIX = "站内信推送异常:";

    private static final String WX_TEMPLATE_ERROR_TIPS_PREFIX = "微信模板推送异常:";

    private static final String NOT_FOUND_ROOM_USER = "未找到绑定的授权用户或业主";

    private final AptBillService aptBillService;

    private final HiveAsClient hiveAsClient;

    private final BUserClient bUserClient;

    private final MessageClient messageClient;

    private final MessageCenterClient messageCenterClient;

    private final DataMigrationConfig dm;

    private final AptBillOperationService operationService;

    private final CUserClient profileClient;

    private final BillPushProperties billPushProperties;

    @Override
    @RedisLock(key = APT_BILL_PUSH_REDIS_KEY_PREFIX, expire = 60L, suffixArgIndexArray = {3, 2})
    public void pushAptBill(Map<String, List<AptBill>> roomBillMap, Set<String> roomIds,
                            AptBillOperator aptBillOperator, String projectId) {
        log.info("pushAptBill is start");
        for (String roomId : roomIds) {
            try {
                refreshCorrelationId();
                pushOneRoomBill(roomId, roomBillMap.get(roomId), aptBillOperator);
            } catch (Exception e) {
                log.warn("房间账单推送异常, roomId: {}", roomId, e);
            }
        }
        log.info("pushAptBill is finish. push room size : {}", roomIds.size());
    }

    @Override
    public Set<BillReceiver> findBillReceivers(List<AptBill> bills) {
        Set<String> distinctRoomIds = new HashSet<>();
        return bills.stream()
                    .filter(bill -> distinctRoomIds.add(bill.getRoomId()))
                    // 不去 hive 校验 roomId 是否被删除， 和账单查询逻辑保持一致
                    .flatMap(bill -> fetchStaffByRoomId(bill.getRoomId()).stream()
                                                                         .map(staff -> BillReceiver.of(bill, staff)))
                    .collect(Collectors.toSet());
    }

    @Override
    @RedisLock(key = APT_BILL_PUSH_REDIS_KEY_PREFIX, expire = 60L, suffixArgIndexArray = {3, 2})
    public void pushUnpaidBill(List<BillNotifyChannel> notifyChannels, List<AptBill> aptBills,
                               AptBillOperator aptBillOperator, String projectId) {
        aptBills.stream()
                .filter(bill1 -> StringUtils.isNotEmpty(bill1.getRoomId()))
                .collect(Collectors.groupingBy(AptBill::getRoomId))
                .forEach((roomId, bills) -> {
                    refreshCorrelationId();

                    FetchRoomResult fetchRoomResult = fetchRoom(roomId);
                    if (fetchRoomResult.hasError()) {
                        onFailedPush(bills, StringUtils.EMPTY, fetchRoomResult.errorMsg(), aptBillOperator,
                                     StringUtils.EMPTY);
                        return;
                    }

                    var billReceivers = fetchBillReceivers(roomId, fetchRoomResult.room());
                    if (billReceivers.isEmpty()) {
                        onFailedPush(bills, StringUtils.EMPTY, NOT_FOUND_ROOM_USER, aptBillOperator, StringUtils.EMPTY);
                        return;
                    }
                    Map<BillReceiver, SendResult> receiverSendSmsResults = new HashMap<>();
                    Map<BillReceiver, SendResult> receiverSendMpResults = new HashMap<>();
                    bills.forEach(bill -> sendBillToReceivers(notifyChannels, bill, billReceivers, aptBillOperator,
                                                              receiverSendSmsResults, receiverSendMpResults));
                });
    }

    @Override
    @RedisLock(key = APT_BILL_PUSH_REDIS_KEY_PREFIX, expire = 60L, suffixArgIndexArray = {4, 3})
    public void pushUnpaidBillForSelectUsers(List<BillNotifyChannel> notifyChannels, List<AptBill> aptBills,
                                             List<BillNotifyRequest.SelectUser> selectUsers,
                                             AptBillOperator aptBillOperator, String projectId) {
        Set<BillNotifyRequest.SelectUser> distinctUserIds = new HashSet<>(selectUsers);
        aptBills.stream()
                .filter(bill1 -> StringUtils.isNotEmpty(bill1.getRoomId()))
                .collect(Collectors.groupingBy(AptBill::getRoomId))
                .forEach((roomId, bills) -> {
                    refreshCorrelationId();

                    FetchRoomResult fetchRoomResult = fetchRoom(roomId);
                    if (fetchRoomResult.hasError()) {
                        onFailedPush(bills, StringUtils.EMPTY, fetchRoomResult.errorMsg(), aptBillOperator,
                                     StringUtils.EMPTY);
                        return;
                    }

                    var room = fetchRoomResult.room();
                    var billReceivers = fetchBillReceivers(roomId, room).stream()
                                                                        .filter(receiver -> distinctUserIds.contains(
                                                                                new BillNotifyRequest.SelectUser(
                                                                                        receiver.getUserId(),
                                                                                        receiver.getRoomId())))
                                                                        .collect(Collectors.toSet());
                    if (billReceivers.isEmpty()) {
                        onFailedPush(bills, StringUtils.EMPTY, NOT_FOUND_ROOM_USER, aptBillOperator, StringUtils.EMPTY);
                        return;
                    }

                    Map<BillReceiver, SendResult> receiverSendSmsResults = new HashMap<>();
                    Map<BillReceiver, SendResult> receiverSendMpResults = new HashMap<>();
                    bills.forEach(bill -> sendBillToReceivers(notifyChannels, bill, billReceivers, aptBillOperator,
                                                              receiverSendSmsResults, receiverSendMpResults));
                });
    }

    public void pushOneRoomBill(String roomId, List<AptBill> roomBills, AptBillOperator aptBillOperator) {
        log.debug("开始推送房间账单，roomId: {}", roomId);

        FetchRoomResult fetchRoomResult = fetchRoom(roomId);
        if (fetchRoomResult.hasError()) {
            onFailedPush(roomBills, StringUtils.EMPTY, fetchRoomResult.errorMsg(), aptBillOperator, StringUtils.EMPTY);
            return;
        }

        var billReceivers = fetchBillReceivers(roomId, fetchRoomResult.room());
        if (billReceivers.isEmpty()) {
            log.warn("未找到绑定的授权用户或业主，roomId：{}", roomId);
            onFailedPush(roomBills, StringUtils.EMPTY, NOT_FOUND_ROOM_USER, aptBillOperator, StringUtils.EMPTY);
            return;
        }
        log.debug("成功获取授权用户和业主信息");

        for (AptBill bill : roomBills) {
            log.debug("开始处理待推送账单 : {}", bill.getId());
            AptBill aptBillNewest = aptBillService.getBillById(bill.getId());
            if (Objects.nonNull(aptBillNewest) && Objects.nonNull(aptBillNewest.getPaymentStatus()) &&
                    HAS_PAID_STATUS.contains(aptBillNewest.getPaymentStatus())) {
                log.info("log_push, bill_already_paid, billNo : {}", aptBillNewest.getBillNo());
                continue;
            }

            List<BillNotifyChannel> selectedChannels = List.of(BillNotifyChannel.WX_TEMPLATE_MESSAGE);
            sendBillToReceivers(selectedChannels, bill, billReceivers, AptBillOperator.PUSH_CONDITIONAL,
                                new HashMap<>(), new HashMap<>());
        }
        log.debug("推送完成. roomId: {}", roomId);
    }

    private static String buildComment(List<SendResult> results) {
        return results.stream()
                      .filter(SendResult::hasError)
                      .map(SendResult::errorMsg)
                      .collect(Collectors.joining("\n"));
    }

    private static String buildAddress(AptBill bill) {
        PositionItemResponse positionItem = bill.getPositionItem();
        return positionItem.getProjectName() + "-" + positionItem.getBuildingName() + "-" + positionItem.getRoomName();
    }

    private static Map<String, Object> buildTemplateParams(BillReceiver receiver, String category) {
        String unitRoomNo = receiver.getBuildingName() + "-" + receiver.getRoomNo();

        Map<String, Object> params = new HashMap<>();
        params.put("thing1", receiver.getProjectName());
        params.put("thing2", unitRoomNo);
        params.put("thing3", category);
        params.put("thing5", "请及时缴纳费用，祝您生活愉快！");
        return params;
    }

    /**
     * 根据房间号去 tenant-admin-service 查询租户.
     * 账单推送前，查询账单对应的租户.
     *
     * @param roomId 房间号
     * @param room   经过hive查询确定有效的房间信息
     * @return
     */
    private Set<BillReceiver> fetchBillReceivers(String roomId, RoomResp room) {
        return fetchStaffByRoomId(roomId).stream()
                                         .map(staff -> BillReceiver.of(room, staff))
                                         .collect(Collectors.toSet());
    }

    private FetchRoomResult fetchRoom(String roomId) {
        RespWrapVo<RoomResp> vo;
        try {
            vo = hiveAsClient.getRoomById(roomId);
        } catch (Exception e) {
            log.warn("fetchRoom: fetch fail : {}", roomId, e);
            return FetchRoomResult.error("查询room[" + roomId + "]异常，请联系系统管理员");
        }
        if (!RespWrapVo.isResponseValidWithData(vo)) {
            log.warn("fetchRoom: no room found: {}", roomId);
            return FetchRoomResult.error("未找到room[" + roomId + "]，请联系系统管理员");
        }
        RoomResp roomResp = vo.getData();
        if (roomResp.getRoom() == null || roomResp.getProject() == null || roomResp.getBuilding() == null) {
            log.warn("fetchRoom: room config invalid: {}", roomId);
            return FetchRoomResult.error("room[" + roomId + "]参数不合法，请联系系统管理员");
        }
        return FetchRoomResult.success(roomResp);
    }

    private void logBillPush(AptBill bill, Set<BillReceiver> billReceivers, List<SendResult> sendResults,
                             AptBillOperator billOperator, List<BillNotifyChannel> notifyChannels) {
        String receivers = billReceivers.stream()
                                        .map(BillReceiver::getPhoneNumber)
                                        .filter(StringUtils::isNotBlank)
                                        .collect(Collectors.joining(COMMA));
        var channels = notifyChannels.stream()
                                     .map(Enum::name)
                                     .collect(Collectors.joining(COMMA));
        if (sendResults.stream()
                       .allMatch(SendResult::hasError)) {
            onFailedPush(List.of(bill), receivers, PUSH_BILL_FAIL_TIPS, billOperator, channels);
        } else {
            String comment = buildComment(sendResults);
            onSuccessPush(bill, receivers, comment, billOperator, channels);
        }
    }

    private void sendBillToReceivers(List<BillNotifyChannel> notifyChannels, AptBill bill,
                                     Set<BillReceiver> billReceivers, AptBillOperator aptBillOperator,
                                     Map<BillReceiver, SendResult> receiverSendSmsResults,
                                     Map<BillReceiver, SendResult> receiverSendMpResults) {
        notifyChannels.forEach(notifyChannel -> {
            if (notifyChannel.equals(BillNotifyChannel.WX_TEMPLATE_MESSAGE)) {
                List<SendResult> results = new ArrayList<>();
                results.add(sendInternalMsg(bill, billReceivers));
                results.add(sendWxTemplateMsg(bill, billReceivers));
                logBillPush(bill, billReceivers, results, aptBillOperator, List.of(notifyChannel));
            }
            if (notifyChannel.equals(BillNotifyChannel.SMS)) {
                var receivers = billReceivers.stream()
                                             .filter(v -> !receiverSendSmsResults.containsKey(v))
                                             .collect(Collectors.toSet());
                SendResult sendResult = sendSms(bill, receivers);
                for (BillReceiver receiver : receivers) {
                    receiverSendSmsResults.put(receiver, sendResult);
                }
                var sendResults = billReceivers.stream()
                                               .map(receiverSendSmsResults::get)
                                               .toList();
                logBillPush(bill, billReceivers, sendResults, aptBillOperator, List.of(notifyChannel));
            }
            if (notifyChannel.equals(BillNotifyChannel.MINI_PROGRAM_NOTIFY)) {
                var receivers = billReceivers.stream()
                                             .filter(v -> !receiverSendMpResults.containsKey(v))
                                             .collect(Collectors.toSet());
                for (BillReceiver receiver : receivers) {
                    SendResult sendResult = sendMiniProgramNotify(bill, receiver);
                    receiverSendMpResults.put(receiver, sendResult);
                }
                var sendResults = billReceivers.stream()
                                               .map(receiverSendMpResults::get)
                                               .toList();
                logBillPush(bill, billReceivers, sendResults, aptBillOperator, List.of(notifyChannel));
            }
        });
    }

    private SendResult sendWxTemplateMsg(AptBill bill, Set<BillReceiver> receivers) {
        List<String> userIds = receivers.stream()
                                        .map(BillReceiver::getUserId)
                                        .toList();
        String keyword3 = buildAddress(bill);

        WxTemplateMsgRequestCommand command = new WxTemplateMsgRequestCommand();
        command.setProjectId(bill.getProjectId());
        command.setBuildingIds(List.of(bill.getBuildingId()));
        command.setUserIds(userIds);
        command.setFirst("尊敬的业主/住户：");
        command.setKeyword1(Optional.ofNullable(bill.getCategory())
                                    .orElse("账单已出，请查看详情并前往缴费"));
        command.setKeyword2(bill.getYear() + "年" + bill.getMonth() + "月");
        command.setKeyword3(keyword3);
        command.setRemark(bill.getAmt() + "元");
        command.setTemplateMsgType(TemplateMsgType.HOA_FEES_REMINDER);
        command.setUrl(dm.getWxMsgUrl());

        try {
            var vo = messageClient.sendWxTemplateMessage(command);
            if (vo.hasError()) {
                log.warn("send_wx_template_message_failed, response : {}", JsonUtils.objToString(vo));
                String wxRespErrorMsg = Optional.of(vo)
                                                .map(RespWrapVo::getMessage)
                                                .orElse(StringUtils.EMPTY);
                return SendResult.error(WX_TEMPLATE_ERROR_TIPS_PREFIX + wxRespErrorMsg);
            }
        } catch (Exception e) {
            log.error("微信模板消息发送异常.", e);
            return SendResult.error(WX_TEMPLATE_ERROR_TIPS_PREFIX + e.getMessage());
        }
        return SendResult.SUCCESS;
    }

    private SendResult sendInternalMsg(AptBill bill, Set<BillReceiver> receivers) {
        String startDate = DateUtils.parseDateToStr(BILL_DATA_PATTERN, bill.getBeginDate());
        String endDate = DateUtils.parseDateToStr(BILL_DATA_PATTERN, bill.getEndDate());
        String content = buildAddress(bill) + ", " + startDate + "~" + endDate + bill.getCategory() + "账单金额为" +
                bill.getAmt() + "元，请您尽快支付，谢谢您的配合！";
        List<String> userIds = receivers.stream()
                                        .map(BillReceiver::getUserId)
                                        .toList();

        MessageDto messageDto = new MessageDto();
        messageDto.setToType("C");
        messageDto.setContent(content);
        messageDto.setUserIds(userIds);
        messageDto.setMessageType(MessageType.BILL_NOTICE);
        messageDto.setParams(Map.of("billId", bill.getId()));

        try {
            var vo = messageCenterClient.sendMessage(messageDto);
            if (vo.hasError()) {
                log.warn("send_internal_message_failed, response : {}", JsonUtils.objToString(vo));
                String mccRespMsgError = Optional.of(vo)
                                                 .map(RespWrapVo::getMessage)
                                                 .orElse(StringUtils.EMPTY);
                return SendResult.error(INTERNAL_MSG_ERROR_TIPS_PREFIX + mccRespMsgError);
            }
        } catch (Exception e) {
            log.warn("站内信发送异常.", e);
            return SendResult.error(INTERNAL_MSG_ERROR_TIPS_PREFIX + e.getMessage());
        }
        return SendResult.SUCCESS;
    }

    private SendResult sendSms(AptBill bill, Set<BillReceiver> receivers) {
        if (receivers.isEmpty()) {
            return SendResult.error("receivers is empty");
        }
        var phoneNumbers = receivers.stream()
                                    .map(BillReceiver::getPhoneNumber)
                                    .toList();
        try {
            //前海嘉里中心-A2-B业主您好，您有账单需缴费，请及时登录KERRY+平台，查看账单明细并缴费
            String content = buildAddress(bill) + "业主您好，您有账单需缴费，请及时登录KERRY+平台，查看账单明细并缴费";

            var request = new SendSmsV2Command();
            request.setPhoneNumbers(phoneNumbers);
            request.setContent(content);
            request.setProjectId(bill.getProjectId());
            request.setSmsType("NOTIFICATION");
            request.setAssetType("APARTMENT");
            messageClient.sendSmsV2(request);
        } catch (Exception e) {
            log.warn("发送短信失败", e);
            return SendResult.error("发送短信失败 : \n" + String.join("\n", phoneNumbers));
        }
        return SendResult.SUCCESS;
    }

    private SendResult sendMiniProgramNotify(AptBill bill, BillReceiver receiver) {
        String query =
                String.format("?roomId=%s&buildingId=%s&page=billPayment&from=miniProgramNotify", bill.getRoomId(),
                              bill.getBuildingId());
        String userId = receiver.getUserId();
        var request = new WxSubscribeSendRequest();
        request.setUserId(userId);
        request.setTemplateId(billPushProperties.getTemplateId());
        request.setTemplateParams(buildTemplateParams(receiver, bill.getCategory()));
        request.setRoutePage(billPushProperties.getMinProgramPath() + query);
        try {
            request.setOpenId(fetchOpenId(userId));

            var vo = messageClient.sendWxSubscribe(request);
            if (vo.success()) {
                return SendResult.SUCCESS;
            }
            log.warn("发送小程序服务通知失败，响应 : {}", JsonUtils.objToString(vo));
        } catch (Exception e) {
            log.warn("发送小程序服务通知失败", e);
        }
        return SendResult.error("发送小程序服务通知失败 : " + receiver.getPhoneNumber());
    }

    private String fetchOpenId(String userId) {
        return profileClient.queryUserOpenIds(billPushProperties.getAppId(), List.of(userId))
                            .stream()
                            .findFirst()
                            .map(WxOpenIDResource::getOpenId)
                            .orElseThrow();
    }

    private List<TenantStaffResponse> fetchStaffByRoomId(String roomId) {
        List<TenantStaffResponse> tenantStaffs = new ArrayList<>();
        tenantStaffs.addAll(fetchStaffs(AUTH_USER, roomId));
        tenantStaffs.addAll(fetchStaffs(OWNER, roomId));
        return tenantStaffs;
    }

    private List<TenantStaffResponse> fetchStaffs(int authorizer, String roomId) {
        RespWrapVo<List<TenantStaffResponse>> staffVo;
        try {
            staffVo = bUserClient.getStaffList(null, authorizer, roomId);
        } catch (Exception e) {
            log.warn("fetchStaffs fail. room : {}", roomId, e);
            return Collections.emptyList();
        }

        if (!RespWrapVo.isResponseValidWithData(staffVo)) {
            log.warn("no room found: {} for authorizer {}", roomId, authorizer);
            return Collections.emptyList();
        }

        var staffs = staffVo.getData();
        if (CollectionUtils.isEmpty(staffs)) {
            log.warn("no room found: {} for authorizer {}", roomId, authorizer);
            return Collections.emptyList();
        }
        return staffs;
    }

    private void onSuccessPush(AptBill bill, String receivers, String comment, AptBillOperator operator,
                               String channels) {
        AptBill originalBill = new AptBill();
        BeanUtils.copyProperties(bill, originalBill);

        BillPushStatus billPushStatus = BillPushStatus.PUSHED;
        BillStatus billStatus = BillStatus.TO_BE_PAID;
        bill.setStatus(billStatus);
        bill.setPushStatus(billPushStatus);
        aptBillService.updateBillStatus4Push(bill.getId(), billPushStatus, billStatus);

        List<OperationChangedFiledRespVo> changedFields = DiffFieldUtils.diffFields(originalBill, bill);
        operationService.saveOperationLog(bill, receivers, changedFields, operator, comment, channels);
        log.debug("账单推送成功. 账单状态更新为待支付. 账单号: {}", bill.getBillNo());
    }

    private void onFailedPush(List<AptBill> bills, String receivers, String comment, AptBillOperator operator,
                              String channels) {
        for (AptBill bill : bills) {
            log.info("账单推送失败，账单号 : {}", bill.getBillNo());
            AptBill originalBill = new AptBill();
            BeanUtils.copyProperties(bill, originalBill);

            bill.setPushStatus(BillPushStatus.PUSH_FAILED);
            aptBillService.updateBillStatus4Push(bill.getId(), BillPushStatus.PUSH_FAILED, null);
            List<OperationChangedFiledRespVo> changedFields = DiffFieldUtils.diffFields(originalBill, bill);
            operationService.saveOperationLog(bill, receivers, changedFields, operator, comment, channels);
        }
    }

    record SendResult(boolean hasError, String errorMsg) {

        public static SendResult SUCCESS = new SendResult(false, null);

        public static SendResult error(String errorMsg) {
            errorMsg = StringUtils.abbreviate(errorMsg, MAX_MSG_LEN);
            return new SendResult(true, errorMsg);
        }

    }

    record FetchRoomResult(boolean hasError, String errorMsg, RoomResp room) {

        public static FetchRoomResult success(RoomResp room) {
            return new FetchRoomResult(false, null, room);
        }

        public static FetchRoomResult error(String errorMsg) {
            errorMsg = StringUtils.abbreviate(errorMsg, MAX_MSG_LEN);
            return new FetchRoomResult(true, errorMsg, null);
        }

    }

}
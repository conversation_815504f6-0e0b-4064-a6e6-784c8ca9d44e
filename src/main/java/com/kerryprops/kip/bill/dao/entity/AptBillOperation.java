package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.bill.dao.convert.AptBillOperatorConverter;
import com.kerryprops.kip.bill.dao.convert.OperationStatusConverter;
import jakarta.persistence.Basic;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import java.io.Serializable;
import java.time.ZonedDateTime;

@Data
@Entity
@Table(name = "tb_apt_bill_operation")
public class AptBillOperation implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Basic
    @Column(name = "bill_id")
    private long billId;

    @Basic
    @Column(name = "bill_no")
    private String billNo;

    @Basic
    @Column(name = "category")
    private String category;

    @Basic
    @Column(name = "project_id")
    private String projectId;

    @Basic
    @Column(name = "building_id")
    private String buildingId;

    @Basic
    @Column(name = "floor_id")
    private String floorId;

    @Basic
    @Column(name = "room_id")
    private String roomId;

    @Basic
    @Column(name = "project_name")
    private String projectName;

    @Basic
    @Column(name = "building_name")
    private String buildingName;

    @Basic
    @Column(name = "floor_name")
    private String floorName;

    @Basic
    @Column(name = "room_name")
    private String roomName;

    @Convert(converter = AptBillOperatorConverter.class)
    @Column(name = "operator")
    private AptBillOperator operator;

    @Basic
    @Column(name = "operation_name")
    private String operationName;

    @Basic
    @Column(name = "comment")
    private String comment;

    @Convert(converter = OperationStatusConverter.class)
    @Column(name = "operation_status")
    private AptBillOperationStatus operationStatus;

    @Basic
    @Column(name = "diff_count")
    private Integer diffCount;

    @Basic
    @Column(name = "operate_user_id")
    private String operateUserId;

    @Basic
    @Column(name = "operate_user_name")
    private String operateUserName;

    @Basic
    @Column(name = "operate_user_email")
    private String operateUserEmail;

    @Basic
    @Column(name = "created_time")
    @CreationTimestamp
    private ZonedDateTime createdTime;

    @Column(name = "notify_receivers")
    private String notifyReceivers;

    @Column(name = "notify_channels")
    private String notifyChannels;

}

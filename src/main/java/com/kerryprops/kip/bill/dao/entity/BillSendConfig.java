package com.kerryprops.kip.bill.dao.entity;

import com.kerryprops.kip.audit.AuditEntity;
import com.kerryprops.kip.audit.AuditField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.time.ZonedDateTime;

@Getter
@Setter
@Builder
@Entity
@Table(name = "kerry_bill_send_config")
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
@AuditEntity
public class BillSendConfig implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @AuditField(alias = "手机号码")
    private Long id;

    /*JDE合同号	len: 32*/
    @AuditField( alias = "JDE合同号")
    @Column(name = "doco")
    private String doco;

    /*手机号码	len: 32*/
    @Column(name = "phone_number")
    @AuditField(alias = "手机号码")
    private String phoneNumber;

    /*租户账号数据库表主键	len: 32*/
    @Column(name = "tenant_manager_id")
    @AuditField(alias = "租户账号数据库表主键")
    private String tenantManagerId;

    /*电子邮箱的用户姓名 len: 128*/
    @AuditField(alias = "电子邮箱用户名")
    @Column(name = "email_username")
    private String emailUsername;

    /*电子邮箱	len: 128*/
    @Column(name = "email")
    @AuditField(alias = "电子邮箱")
    private String email;

    /*B端登录账号	len: 64*/
    @Column(name = "login_no")
    @AuditField(alias = "B端登录账号")
    private String loginNo;

    /* 楼盘id	len: 32 */
    @Column(name = "project_id")
    @AuditField(alias = "楼盘id")
    private String projectId;

    /* 楼栋id	len: 128 */
    @Column(name = "building_id")
    @AuditField(alias = "楼栋id")
    private String buildingId;

    /* 楼栋名称 len: 128 */
    @Column(name = "building_name")
    @AuditField(alias = "楼栋名称")
    private String buildingName;

    /*建筑物编号	len: 100*/
    @Column(name = "mcu")
    @AuditField(alias = "建筑物编号")
    private String mcu;

    /*JDE单元	len: 200*/
    @Column(name = "unit")
    @AuditField(alias = "JDE单元")
    private String unit;

    /**
     * 更新人
     * len: 64
     */
    @Column(name = "manual_update_operator")
    @AuditField(alias = "更新人")
    private String manualUpdateOperator;

    /**
     * 更新时间
     */
    @Column(name = "manual_update_time")
    @AuditField(alias = "更新时间")
    private ZonedDateTime manualUpdateTime;

    /*删除标志	len: 1*/
    @Column(name = "is_del")
    @AuditField(alias = "删除标志")
    private Integer isDel;

    @AuditField(alias = "创建时间")
    @Column(name = "created_time", insertable = false, updatable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime createdTime;

    @AuditField(alias = "更新时间")
    @Column(name = "updated_time", insertable = false, columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private ZonedDateTime updatedTime;

}

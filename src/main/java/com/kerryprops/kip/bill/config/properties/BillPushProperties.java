package com.kerryprops.kip.bill.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * BillPushProperties.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> 2025-06-19 14:29:11
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "bill.push")
public class BillPushProperties {

    private String appId = "wx92c3e55fbef6b2af";

    private String templateId = "Z2aW5XguhdQ6tSZbox9m0Cnqq2XSqk_-5-bo8V2Kdb0";

    private String minProgramPath = "/pages/package-bill/redirectTo";

}

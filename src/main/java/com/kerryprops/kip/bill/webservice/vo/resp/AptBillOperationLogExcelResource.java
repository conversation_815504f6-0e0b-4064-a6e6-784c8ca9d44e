package com.kerryprops.kip.bill.webservice.vo.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class AptBillOperationLogExcelResource {

    @ExcelProperty("日志编号")
    private long id;

    @ExcelProperty("楼盘")
    private String projectName;

    @ExcelProperty("楼栋")
    private String buildingName;

    @ExcelProperty("户号")
    private String floorRoom;

    @ExcelProperty("账单号")
    private String billNo;

    @ExcelProperty("费项")
    private String category;

    @ExcelProperty("日期")
    private String operationTime;

    @ExcelProperty("操作内容")
    private String changedContent;

    @ExcelProperty("执行人")
    private String operationUserName;

    @ExcelProperty("发送对象")
    private String notifyReceivers;

    @ExcelProperty("推送类型")
    private String notifyChannels;

    @ExcelProperty("推送状态")
    private String operationStatus;

    @ExcelProperty("备注")
    private String comment;

}
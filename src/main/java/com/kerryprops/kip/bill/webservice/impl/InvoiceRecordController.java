package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.fastjson.JSON;
import com.kerryprops.kip.bill.common.enums.BillPayModule;
import com.kerryprops.kip.bill.common.enums.FeeTypeEnum;
import com.kerryprops.kip.bill.common.enums.InvoicedStatus;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.enums.SystemOrigTypeEnum;
import com.kerryprops.kip.bill.common.utils.InvoiceUtils;
import com.kerryprops.kip.bill.common.utils.StringFormatUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.KerryInvoiceProperties;
import com.kerryprops.kip.bill.dao.EFapiaoSyncRepository;
import com.kerryprops.kip.bill.dao.entity.AptPaymentInfo;
import com.kerryprops.kip.bill.dao.entity.EFapiaoJDEBill;
import com.kerryprops.kip.bill.dao.entity.EFapiaoSyncBill;
import com.kerryprops.kip.bill.dao.entity.InvoiceRecord;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.KipInvoiceClient;
import com.kerryprops.kip.bill.feign.entity.UploadInvoiceDetailV2Vo;
import com.kerryprops.kip.bill.feign.entity.UploadInvoiceMainV2Vo;
import com.kerryprops.kip.bill.service.PaymentBillService;
import com.kerryprops.kip.bill.service.impl.EFapiaoBillServiceImpl;
import com.kerryprops.kip.bill.service.impl.EFapiaoJdeBillServiceImpl;
import com.kerryprops.kip.bill.service.impl.InvoiceApplicationService;
import com.kerryprops.kip.bill.utils.BillUtil;
import com.kerryprops.kip.bill.webservice.InvoiceRecordFacade;
import com.kerryprops.kip.bill.webservice.vo.req.CallBackKerryInvoiceMainVo;
import com.kerryprops.kip.bill.webservice.vo.req.InvoiceUploadResource;
import com.kerryprops.kip.bill.webservice.vo.resp.AptBillVo;
import com.kerryprops.kip.bill.webservice.vo.resp.AptPaymentInfoVo;
import com.kerryprops.kip.bill.webservice.vo.resp.PositionItemResponse;
import com.kerryprops.kip.hiveas.feign.dto.resp.BuildingRespDto;
import com.kerryprops.kip.hiveas.webservice.vo.resp.BuildingResponseVo;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.DATE_FORMAT_YYYY_MM_DD;
import static com.kerryprops.kip.bill.common.enums.BillInvoiceBizTypeEnum.SY_UTILITY;
import static com.kerryprops.kip.bill.common.utils.NonNullUtils.nonNullStringFunction;

/**
 * @ClassName: InvoiceRecordController
 * @Author: Kanon
 * @Date: 2022/2/22 13:49
 * @Version: 2.0
 */
@Slf4j
@RestController
public class InvoiceRecordController implements InvoiceRecordFacade {

    private static final String BLANK_SIGN = " ";

    @Value("${e-fapiao.sales-bill-type}")
    private String eFapiaoSalesBillType;

    @Value("${e-fapiao.system-orig}")
    private String eFapiaoSystemOrig;

    @Value("${e-fapiao.sales-bill-no-prefix}")
    private String eFapiaoSalesBillNoPrefix;

    @Value("${e-fapiao.sales-bill-no-suffix-len}")
    private int eFapiaoSalesBillNoSuffixLen;

    @Value("${invoice-system-orig-manual-sales-bill-no-prefix}")
    private String invoiceSystemOrigManualSalesbillNoPrefix;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Autowired
    private EFapiaoSyncRepository eFapiaoSyncRepository;

    @Autowired
    private PaymentBillService paymentBillService;

    @Autowired
    private KipInvoiceClient kipInvoiceClient;

    @Autowired
    private KerryInvoiceProperties kerryInvoiceProperties;

    @Autowired
    private InvoiceApplicationService invoiceApplicationService;

    @Autowired
    private EFapiaoBillServiceImpl eFapiaoBillService;

    @Autowired
    private EFapiaoJdeBillServiceImpl eFapiaoJdeBillService;

    @Override
    public RespWrapVo<InvoiceRecord> invoiceUpload(@Valid @RequestBody InvoiceUploadResource resource) {
        log.info("received_invoice_upload_req_resource: {}", JSON.toJSONString(resource));
        String paymentId = resource.getOrderNo();
        AptPaymentInfoVo aptPaymentInfoVo = paymentBillService.queryPaymentInfoById(paymentId);

        UploadInvoiceMainV2Vo uploadInvoiceMainVo = parseInvoiceData(resource, aptPaymentInfoVo);
        String sellerName = uploadInvoiceMainVo.getSellerName();
        uploadInvoiceMainVo.setSellerName(null);
        log.info("received_invoice_upload_req_vo: {}", JSON.toJSONString(uploadInvoiceMainVo));

        RespWrapVo<Object> respWrapVo = kipInvoiceClient.invoiceUploadV2(uploadInvoiceMainVo);
        InvoiceRecord invoiceRecord;
        if (!Objects.equals(respWrapVo.getCode(), RespCodeEnum.SUCCESS.getCode())) {
            throw new RuntimeException(respWrapVo.getMessage());
        } else {
            // 更新已提交开票申请信息
            paymentBillService.applyInvoice(resource.getOrderNo());
            // 保存开票记录
            invoiceRecord = invoiceApplicationService.saveInvoiceRecord(uploadInvoiceMainVo, sellerName, resource.getAreaCode());
        }
        return new RespWrapVo<>(invoiceRecord);
    }

    @Override
    public RespWrapVo<InvoiceRecord> getInvoiceRecord(@RequestParam("orderNo") String orderNo) {
        InvoiceRecord record = invoiceApplicationService.getInvoiceRecordByOrderNo(orderNo);
        return new RespWrapVo<>(record);
    }

    @Override
    public XForceResponse invoiceCallback(@RequestBody CallBackKerryInvoiceMainVo mainVo) {
        log.info("收到开票回调请求，回调参数: {}", JSON.toJSONString(mainVo));

        // 页面导入场景
        if (SystemOrigTypeEnum.PAGE_IMPORT.getCode().equals(mainVo.getSystemOrigType())
                || SY_UTILITY.name().equalsIgnoreCase(mainVo.getSystemOrig())) {
            log.info("更新发票信息 导入开票场景，处理开始");
            eFapiaoBillService.excelImportInvoiceWriteBack(mainVo);
            log.info("更新发票信息 导入开票场景，处理完成");
            return XForceResponse.success();
        }

        // 单据类型为JDE-Manual，或手工开票（预开票）的JDE场景
        if (eFapiaoSalesBillType.equals(mainVo.getSalesbillType())
                || (SystemOrigTypeEnum.MANUAL.getCode().equals(mainVo.getSystemOrigType())
                && eFapiaoSystemOrig.equalsIgnoreCase(mainVo.getSystemOrig()))) {
            log.info("更新发票信息 JDE同步开票场景，处理开始");
            eFapiaoInvoiceCallback(mainVo);
            log.info("更新发票信息 JDE同步开票场景，处理完成");
            return XForceResponse.success();
        }

        log.info("更新发票信息 物业缴费开票场景，处理开始");
        String orderNo = mainVo.getSalesbillNo();
        if (0 == mainVo.getInvoiceStatus()) {
            paymentBillService.cancelInvoice(orderNo);
            // 回写发票错误信息
            invoiceApplicationService.updateInvoiceRecord(null, mainVo);
        } else if (1 == mainVo.getInvoiceStatus() && StringUtils.isNotBlank(mainVo.getPdfUrl())) {
            AptPaymentInfo aptPaymentInfo = paymentBillService.invoiceCallback(orderNo, mainVo);
            // 回填发票记录信息
            invoiceApplicationService.updateInvoiceRecord(aptPaymentInfo, mainVo);
            invoiceApplicationService.updateInvoicedStatus(orderNo, InvoicedStatus.HAS_INVOICED);
        }
        log.info("更新发票信息 物业缴费开票场景，处理完成");
        return XForceResponse.success();
    }

    private void eFapiaoInvoiceCallback(CallBackKerryInvoiceMainVo mainVo) {
        if (SystemOrigTypeEnum.MANUAL.getCode().equals(mainVo.getSystemOrigType())) {
            log.info("手工开票场景，开始处理");

            if (StringUtils.isNotEmpty(mainVo.getSalesbillNo())
                    && mainVo.getSalesbillNo().startsWith(invoiceSystemOrigManualSalesbillNoPrefix)) {
                // 手工开票（预开票）场景，如将业务单转化成不开票单据，则去除票易通添加的“Z”
                log.info("手工开票场景，去除票易通添加的前缀");
                mainVo.setSalesbillNo(mainVo.getSalesbillNo().substring(Optional
                        .ofNullable(invoiceSystemOrigManualSalesbillNoPrefix).map(String::length).orElse(0)));
            }
        }

        if (StringUtils.isNotEmpty(mainVo.getSalesbillNo()) && mainVo.getSalesbillNo().startsWith(eFapiaoSalesBillNoPrefix)) {
            log.info("处理业务单号在Kerry+添加的前缀后准");
            String newSalesbillNo = mainVo.getSalesbillNo().substring(Optional
                            .ofNullable(eFapiaoSalesBillNoPrefix).map(String::length).orElse(0)
                    , mainVo.getSalesbillNo().length() - eFapiaoSalesBillNoSuffixLen);
            mainVo.setSalesbillNo(newSalesbillNo);
        }

        if (SystemOrigTypeEnum.MANUAL.getCode().equals(mainVo.getSystemOrigType())) {
            // 手工开票（预开票）场景，填充mcu、jdeUnit字段
            log.info("手工开票（预开票）场景，填充MCU和JDE单元字段开始");
            EFapiaoJDEBill eFapiaoJDEBill = eFapiaoJdeBillService.queryBySalesBillNo(mainVo.getSalesbillNo());

            if (Objects.isNull(eFapiaoJDEBill)) {
                throw new RuntimeException(String
                        .format("e_fapiao_write_back_invoice e_fapiao_jde_bill_disappear, sales_bill_no: {}"
                                , mainVo.getSalesbillNo()));
            }
            mainVo.setBusinessType(nonNullStringFunction.apply(eFapiaoJDEBill.getMcu()));
            mainVo.setExt1(nonNullStringFunction.apply(eFapiaoJDEBill.getJdeUnit()));
            mainVo.setExt2(nonNullStringFunction.apply(String.valueOf(eFapiaoJDEBill.getDoco())));
            mainVo.setExt3(nonNullStringFunction.apply(eFapiaoJDEBill.getAn8()));
            log.info("手工开票（预开票）场景，填充MCU和JDE单元字段完成");
        } else if (eFapiaoSalesBillType.equals(mainVo.getSalesbillType())) {
            // JDE同步场景，填充mcu、jdeUnit字段
            log.info("JDE同步场景，填充MCU和JDE单元字段开始");
            EFapiaoSyncBill eFapiaoSyncBill = eFapiaoSyncRepository.findTopBySalesBillNo(mainVo.getSalesbillNo());

            if (Objects.isNull(eFapiaoSyncBill)) {
                throw new RuntimeException(String
                        .format("e_fapiao_write_back_invoice e_fapiao_sync_bill_disappear, sales_bill_no: {}"
                                , mainVo.getSalesbillNo()));
            }
            mainVo.setBusinessType(nonNullStringFunction.apply(eFapiaoSyncBill.getMcu()));
            mainVo.setExt1(nonNullStringFunction.apply(eFapiaoSyncBill.getJdeUnit()));
            mainVo.setExt2(nonNullStringFunction.apply(String.valueOf(eFapiaoSyncBill.getDoco())));
            mainVo.setExt3(nonNullStringFunction.apply(eFapiaoSyncBill.getAn8()));
            log.info("JDE同步场景，填充MCU和JDE单元字段完成");
        }

        // 数据回写到JDE
        eFapiaoJdeBillService.writeBackInvoice2Jde(mainVo);

        // 更新e-fapiao同步表
        eFapiaoBillService.invoiceWriteBack(mainVo);
    }

    private UploadInvoiceMainV2Vo parseInvoiceData(InvoiceUploadResource resource, AptPaymentInfoVo paymentInfoVo) {

        if (paymentInfoVo.getAppliedInvoice() == 2) {
            throw new RuntimeException("当前账单已开票");
        }

        // 场景：物业杂费、物业缴费、预收款、物业缴费+收款
        boolean hasPrepaid = BigDecimal.ZERO.compareTo(paymentInfoVo.getAdvanceAmount()) < 0;
        boolean hasFeePay = BillPayModule.CASHIER_FEE.equals(paymentInfoVo.getBillPayModule())
                && Objects.nonNull(paymentInfoVo.getFeeId()) && paymentInfoVo.getFeeId() > 0;

        // 预收款、杂费是否可开票（税收分类编码已配置）
        final String INVOICABLE = "1";
        boolean isInvoicable = INVOICABLE.equals(paymentInfoVo.getIsAdvanceBilling());

        // 过滤出可以开票的账单
        List<AptBillVo> aptBillList = paymentInfoVo.getAptBillList().stream()
                .filter(b -> b.getIsBilling().equals("1")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(aptBillList) && !(hasPrepaid && isInvoicable)
                && !(hasFeePay && isInvoicable)) {
            throw new RuntimeException("当前账单不支持开票");
        }

        String buildingId = CollectionUtils.isNotEmpty(aptBillList)
                ? aptBillList.get(0).getBuildingId() : paymentInfoVo.getBuildingId();

        BuildingResponseVo buildingResponseVo = hiveAsClient.getBuildingById(buildingId);
        if (Objects.isNull(buildingResponseVo) || Objects.isNull(buildingResponseVo.getBuilding())) {
            throw new RuntimeException("Co not configured for building " + aptBillList.get(0).getBuildingId());
        }

        BuildingRespDto building = buildingResponseVo.getBuilding();

        FeeTypeEnum feeType = hasFeePay ? FeeTypeEnum.PROPERTY_FEE : FeeTypeEnum.PROPERTY_MANAGEMENT;

        UploadInvoiceMainV2Vo mainVo = new UploadInvoiceMainV2Vo();
        mainVo.setFeeType(feeType.name());
        mainVo.setSalesbillNo(resource.getOrderNo());
        mainVo.setInvoiceType(resource.getInvoiceType());
        // 备注信息：第一行：系统订单号+收款号+楼盘名+楼栋+户号，第二行：填写的备注信息
        PositionItemResponse positionItem = paymentInfoVo.getPositionItem();
        mainVo.setRemark(resource.getOrderNo()
                + BLANK_SIGN + paymentInfoVo.getPayAct()
                + BLANK_SIGN + Optional.ofNullable(positionItem).map(PositionItemResponse::getProjectName).orElse(StringUtils.EMPTY)
                + BLANK_SIGN + Optional.ofNullable(positionItem).map(PositionItemResponse::getBuildingName).orElse(StringUtils.EMPTY)
                + BLANK_SIGN + Optional.ofNullable(positionItem).map(PositionItemResponse::getRoomName).orElse(StringUtils.EMPTY)
                + "\n" + resource.getRemark());
        // 报修，物业公司开票
        mainVo.setSellerNo(building.getPropertyManagementCo());
        mainVo.setSellerName(building.getPropertyManagementName());
        // 接受人邮箱地址
        if (StringFormatUtils.isEmailFormat(resource.getEmail())) {
            mainVo.setReceiveUserEmail(resource.getEmail());
        } else {
            mainVo.setReceiveUserTel(resource.getEmail());
        }
        // 填充购买人信息
        mainVo.setPurchaserName(resource.getInvoiceTitle());
        mainVo.setPurchaserAddress(resource.getAddress());
        mainVo.setPurchaserBankAccount(resource.getBankAccount());
        mainVo.setPurchaserBankName(resource.getBankName());
        mainVo.setPurchaserTaxNo(resource.getTaxNo());
        mainVo.setPurchaserTel(resource.getTel());
        // 含税价格
        mainVo.setPriceMethod(1);
        // 总金额
        mainVo.setAmountWithTax(BillUtil.formatAmount(paymentInfoVo.getCanInvoiceBillAmount()));
        mainVo.setSystemId(kerryInvoiceProperties.getSystemId());
        mainVo.setTimeStamp((new Date()).getTime() + "");
        // 填充签名参数
        mainVo.setSign(InvoiceUtils.getSign(mainVo.getSalesbillNo(), mainVo.getTimeStamp(), kerryInvoiceProperties));
        // ext1传收费类型
        mainVo.setExt1(feeType.getDesc());
        mainVo.setProjectName(building.getName());
        if (org.apache.commons.lang3.StringUtils.isBlank(building.getPropertyManagementCo())) {
            throw new RuntimeException("building not config property management co, buildingId: " + building.getId());
        }

        // 开票类型
        //强制自动开票
        List<UploadInvoiceDetailV2Vo> detailVoList = new ArrayList<>();
        for (AptBillVo aptBillVo : aptBillList) {
            // 开票明细
            UploadInvoiceDetailV2Vo detailVo = new UploadInvoiceDetailV2Vo();
            detailVo.setSalesbillItemNo(aptBillVo.getBillNo());
            detailVo.setQuantity(BigDecimal.ONE);
            detailVo.setAmountWithTax(BigDecimal.valueOf(aptBillVo.getAmt()).setScale(2, RoundingMode.HALF_UP));
            detailVo.setUnitPriceWithTax(BigDecimal.valueOf(aptBillVo.getAmt()).setScale(2, RoundingMode.HALF_UP));
            detailVo.setBillCode(aptBillVo.getRdGlc());


            String beginDate = Optional.ofNullable(aptBillVo.getBeginDate())
                    .map(e -> DateFormatUtils.format(e, DATE_FORMAT_YYYY_MM_DD)).orElse(StringUtils.EMPTY);
            String endDate = Optional.ofNullable(aptBillVo.getEndDate())
                    .map(e -> DateFormatUtils.format(e, DATE_FORMAT_YYYY_MM_DD)).orElse(StringUtils.EMPTY);
            detailVo.setItemSpec(beginDate + " - " + endDate);

            detailVoList.add(detailVo);
        }

        if (hasPrepaid && isInvoicable) {
            // 开票明细-预收款
            UploadInvoiceDetailV2Vo detailVo = new UploadInvoiceDetailV2Vo();
            detailVo.setSalesbillItemNo(resource.getOrderNo() + "001");
            detailVo.setQuantity(BigDecimal.ONE);
            detailVo.setAmountWithTax(BillUtil.formatAmount(paymentInfoVo.getAdvanceAmount()));
            detailVo.setUnitPriceWithTax(BillUtil.formatAmount(paymentInfoVo.getAdvanceAmount()));
            detailVo.setBillCode(paymentInfoVo.getPaymentCate().getCode());
            detailVoList.add(detailVo);
        }

        if (hasFeePay && isInvoicable) {
            // 开票明细-杂费
            BigDecimal amt = BillUtil.formatAmount(BigDecimal.valueOf(paymentInfoVo.getAmt()));

            UploadInvoiceDetailV2Vo detailVo = new UploadInvoiceDetailV2Vo();
            detailVo.setSalesbillItemNo(resource.getOrderNo() + "002");
            detailVo.setQuantity(BigDecimal.ONE);
            detailVo.setAmountWithTax(amt);
            detailVo.setUnitPriceWithTax(amt);
            detailVo.setBillCode(String.valueOf(paymentInfoVo.getFeeId())); // 杂费-费项
            detailVoList.add(detailVo);
        }

        mainVo.setDetailVos(detailVoList);
        return mainVo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XForceResponse implements Serializable {

        private int code;

        private String message;

        public static XForceResponse success() {
            return new XForceResponse(200, "success");
        }

    }

}

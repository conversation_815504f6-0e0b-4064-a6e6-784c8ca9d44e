package com.kerryprops.kip.bill.webservice;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.service.model.s.BillReceiver;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillExportReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillManageSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillNotifyRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillManageRespVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;
import java.util.Map;

/***********************************************************************************************************************
 * Project - accelerator
 * (c) Kerry Properties Limited. All rights reserved.
 * Author - David Wei
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/
@Tag(name = "公寓小区-S端账单管理")
@RequestMapping(value = "/s/apt/bill", produces = MediaType.APPLICATION_JSON_VALUE)
public interface StaffAptBillFacade {

    @GetMapping("/room/queryPaymentStatus")
    @Operation(summary = "获取账单支付状态下拉选项")
    RespWrapVo<Map<String, String>> queryPaymentStatus();

    @GetMapping("/room/queryAlphs/{projectId}")
    @Operation(summary = "获取付款人下拉选项")
    RespWrapVo<List<String>> queryAlphs(@PathVariable("projectId") String projectId, String roomId);

    @GetMapping("/room/queryCategorys/{projectId}")
    @Operation(summary = "获取费项下拉选项")
    RespWrapVo<List<String>> queryCategorys(@PathVariable("projectId") String projectId, String roomId);

    @GetMapping("/room/bills")
    @Operation(summary = "查询电子账单明细列表")
    RespWrapVo<Page<StaffAptBillManageRespVo>> list(@SortDefault.SortDefaults(
            value = {@SortDefault(sort = "year", direction = Sort.Direction.DESC),
                    @SortDefault(sort = "month", direction = Sort.Direction.DESC),
                    @SortDefault(sort = "beginDate", direction = Sort.Direction.ASC),
                    @SortDefault(sort = "endDate", direction = Sort.Direction.ASC)
            }) Pageable pageable, @ModelAttribute AptBillManageSearchReqVo vo);

    @GetMapping("/room/get-users-by-selected-bills")
    @Operation(summary = "查询已选择电子账单关联的用户")
    RespWrapVo<List<BillReceiver>> findBillReceivers(@ModelAttribute AptBillManageSearchReqVo vo);

    @PostMapping("/push/{projectId}")
    @Operation(summary = "推送账单", description = """
            异步推送指定楼盘已支付的账单给账单对应roomId的租户
            1、只会推送不超过下个月的账单
            2、只推送已支付(`PAID`)和直接扣款已支付(`DIRECT_DEBIT_PAID`)状态的账单
            """)
    RespWrapVo<Boolean> pushAll(@PathVariable("projectId") String projectId);

    @PostMapping("/pushAll")
    @Operation(summary = "推送账单（带过滤条件全量推送）", description = """
            异步推送所有覆盖筛选条件账单给账单对应roomId的租户
            会剔除已支付成功和代扣成功的账单。
            """)
    RespWrapVo<Boolean> pushConditional(@RequestBody AptBillManageSearchReqVo vo);

    @PostMapping("/notifications/send")
    @Operation(summary = "推送账单通知", description = """
            【账单推送-发送】支持3种推送模式、带过滤条件推送.
            只推送未支付的账单给符合条件的租户（会剔除已支付成功和代扣成功的账单）
            """)
    RespWrapVo<Boolean> notify(@RequestBody BillNotifyRequest request);

    @PostMapping("/pushSelected")
    @Operation(summary = "推送账单（多选场景)", description = """
            异步推送指定账单给账单对应roomId的租户.
            只推送已支付(`PAID`)和直接扣款已支付(`DIRECT_DEBIT_PAID`)状态的账单
            """)
    RespWrapVo<Boolean> pushSelected(@RequestBody List<Long> billIds);

    @PostMapping("/export")
    @Operation(summary = "导出账单")
    void export(@RequestBody AptBillExportReqVo exportReqVo, HttpServletResponse response);

}

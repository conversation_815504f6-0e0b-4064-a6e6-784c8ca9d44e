package com.kerryprops.kip.bill.webservice.vo.req;

import com.kerryprops.kip.bill.common.enums.BillNotifyChannel;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillPushStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Data
@Schema
public class BillNotifyRequest extends BaseReqVo {

    @Schema(title = "账单ID")
    private Long id;

    @Schema(title = "付款人")
    private String alph;

    @Schema(title = "账单号")
    private String billNo;

    @Schema(title = "费项")
    private List<String> categorys;

    @Schema(title = "账单年开始")
    private Integer beginYear;

    @Schema(title = "账单年结束")
    private Integer endYear;

    @Schema(title = "账单月开始")
    private Integer beginMonth;

    @Schema(title = "账单月结束")
    private Integer endMonth;

    @Schema(title = "账单年")
    private Integer year;

    @Schema(title = "账单月")
    private Integer month;

    @Schema(title = "系统状态")
    private BillStatus status;

    @Schema(title = "用户支付状态")
    private BillPaymentStatus paymentStatus;

    @Schema(title = "账单推送状态")
    private BillPushStatus pushStatus;

    @Schema(title = "楼盘ids")
    private List<String> projectIds;

    @Schema(title = "楼栋ID")
    private List<String> buildingIds;

    @Schema(title = "楼层ID")
    private List<String> floorIds;

    @Schema(title = "房间ID")
    private List<String> roomIds;

    @NotEmpty
    @Schema(title = "账单推送渠道", description = "可多选")
    private List<BillNotifyChannel> notifyChannels;

    @Schema(title = "选中的用户id", description = "全选时，可不传递该值")
    private List<SelectUser> selectUsers;

    @Schema(title = "是否全选推送")
    private Boolean isSendAllUser;

    public record SelectUser(String userId, String roomId) {

    }

}

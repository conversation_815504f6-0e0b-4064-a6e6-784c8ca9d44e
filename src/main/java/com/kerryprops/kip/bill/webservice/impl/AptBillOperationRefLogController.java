package com.kerryprops.kip.bill.webservice.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.kerryprops.kip.bill.common.constants.AppConstants;
import com.kerryprops.kip.bill.common.enums.BillNotifyChannel;
import com.kerryprops.kip.bill.common.exceptions.RestInvalidParamException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.dao.entity.AptBillOperation;
import com.kerryprops.kip.bill.dao.entity.AptBillOperationContent;
import com.kerryprops.kip.bill.dao.entity.AptBillOperationStatus;
import com.kerryprops.kip.bill.service.AptBillOperationService;
import com.kerryprops.kip.bill.service.model.s.AptBillOperationBo;
import com.kerryprops.kip.bill.webservice.vo.req.BillRefLogSearchRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.AptBillOperationLogExcelResource;
import com.kerryprops.kip.bill.webservice.vo.resp.AptBillOperationLogResource;
import com.kerryprops.kip.bill.webservice.vo.resp.OperationChangedFiledRespVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.kerryprops.kip.bill.common.enums.RespCodeEnum.EMPTY_CONTENT_EXPORT_EXCEL;
import static com.kerryprops.kip.bill.common.utils.BillingFun.excelZonedDatetimeFun;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exceptionToNull;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exportByEasyExcel;

@Tag(name = "账单操作日志")
@RestController
@Slf4j
public class AptBillOperationRefLogController {

    private final ObjectMapper objectMapper;

    private final AptBillOperationService operationService;

    /**
     * db domain convert to LogResource
     */
    private final BiFunction<AptBillOperation, Map<Long, List<AptBillOperationContent>>, AptBillOperationLogResource>
            mappingToVo;

    public AptBillOperationRefLogController(ObjectMapper objectMapper, AptBillOperationService operationService) {
        this.objectMapper = objectMapper;
        this.operationService = operationService;
        mappingToVo = (op, map) -> {
            AptBillOperationLogResource a = new AptBillOperationLogResource();
            a.setBillNo(op.getBillNo());
            a.setBuildingName(op.getBuildingName());
            a.setFloorName(op.getFloorName());
            a.setProjectName(op.getProjectName());
            a.setRoomName(op.getRoomName());
            a.setId(op.getId());
            a.setBillId(op.getBillId());
            a.setCategory(op.getCategory());
            a.setOperation(op.getOperator());
            a.setOperationTime(op.getCreatedTime());
            a.setOperationUserId(op.getOperateUserId());
            a.setOperationUserName(op.getOperateUserName());
            a.setOperationUserEmail(op.getOperateUserEmail());
            a.setOperationStatus(op.getOperationStatus());
            a.setComment(op.getComment());
            a.setNotifyReceivers(op.getNotifyReceivers());
            a.setNotifyChannels(op.getNotifyChannels());
            List<AptBillOperationContent> lst = map.get(op.getId());
            if (CollectionUtils.isNotEmpty(lst)) {
                a.setChangedContent(lst.stream()
                                       .map(e -> BeanUtil.copy(e, OperationChangedFiledRespVo.class))
                                       .collect(Collectors.toList()));
            } else {
                a.setChangedContent(Collections.emptyList());
            }
            return a;
        };
    }

    /**
     * query operation reference logs
     */
    @Operation(summary = "账单操作日志查询接口")
    @GetMapping("/s/apt/bill/reflog")
    public Page<AptBillOperationLogResource> billsOperationLog(
            @SortDefault.SortDefaults({@SortDefault(sort = "createdTime", direction = Sort.Direction.DESC)})
            Pageable pageable, BillRefLogSearchRequest request) {

        log.info("query_reflog_request: {}", exceptionToNull(() -> objectMapper.writeValueAsString(request)));
        AptBillOperationBo bo = BeanUtil.copy(request, AptBillOperationBo.class);

        Page<AptBillOperation> operations = operationService.queryOperationLogs(bo, pageable);
        Map<Long, List<AptBillOperationContent>> contentMap = getContentMap(operations.stream());
        return operations.map(op -> mappingToVo.apply(op, contentMap));
    }

    /**
     * export log excel
     */
    @Operation(summary = "账单操作日志导出接口")
    @GetMapping("/s/apt/bill/reflog/export")
    public void billsOperationLogExport(BillRefLogSearchRequest request, HttpServletResponse response) {
        log.info("export_reflog_request: {}", exceptionToNull(() -> objectMapper.writeValueAsString(request)));
        AptBillOperationBo bo = BeanUtil.copy(request, AptBillOperationBo.class);
        List<AptBillOperation> operations = operationService.queryOperationLogs(bo);
        if (CollectionUtils.isEmpty(operations)) {
            throw new RestInvalidParamException(EMPTY_CONTENT_EXPORT_EXCEL);
        }
        Map<Long, List<AptBillOperationContent>> contentMap = getContentMap(operations.stream());
        var excelResources = operations.stream()
                                       .map(operation -> {
                                           var excelResource =
                                                   BeanUtil.copy(operation, AptBillOperationLogExcelResource.class);
                                           String notifyChannels = operation.getNotifyChannels();
                                           String notifyReceivers = operation.getNotifyReceivers();
                                           excelResource.setNotifyChannels(transChannel(notifyChannels));
                                           excelResource.setNotifyReceivers(transReceivers(notifyReceivers));
                                           excelResource.setFloorRoom(operation.getRoomName());
                                           excelResource.setOperationUserName(operation.getOperateUserName());
                                           excelResource.setOperationTime(
                                                   excelZonedDatetimeFun.apply(operation.getCreatedTime()));
                                           List<AptBillOperationContent> operationContents =
                                                   contentMap.get(operation.getId());
                                           if (CollectionUtils.isNotEmpty(operationContents)) {
                                               String[] changeArr = operationContents.stream()
                                                                                     .map(c -> c.getFieldAlias() + "：" +
                                                                                             c.getFieldOldValue() +
                                                                                             " -> " +
                                                                                             c.getFieldNewValue())
                                                                                     .toArray(String[]::new);
                                               excelResource.setChangedContent(StringUtils.join(changeArr, "\r\n"));
                                           }
                                           if (AptBillOperationStatus.UNKNOWN.equals(operation.getOperationStatus())) {
                                               excelResource.setOperationStatus(null);
                                           }
                                           return excelResource;
                                       })
                                       .collect(Collectors.toList());

        final String CONTENT_NAME = "apt_bill_operation_log";
        exportByEasyExcel(response, excelResources, AptBillOperationLogExcelResource.class, CONTENT_NAME, CONTENT_NAME);
    }

    private static String transReceivers(String notifyReceivers) {
        if (StringUtils.isBlank(notifyReceivers)) {
            return notifyReceivers;
        }
        return String.join("\n", StringUtils.split(notifyReceivers, AppConstants.COMMA));
    }

    private static String transChannel(String notifyChannels) {
        if (StringUtils.isBlank(notifyChannels)) {
            return notifyChannels;
        }
        return Arrays.stream(StringUtils.split(notifyChannels, AppConstants.COMMA))
                     .map(BillNotifyChannel::valueOf)
                     .map(BillNotifyChannel::getTitle)
                     .collect(Collectors.joining("\n"));
    }

    /**
     * mapping operation id to contents
     */
    private Map<Long, List<AptBillOperationContent>> getContentMap(Stream<AptBillOperation> operations) {
        List<Long> opIds = operations.filter(op -> op.getDiffCount() > 0)
                                     .map(AptBillOperation::getId)
                                     .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(opIds)) {
            return Collections.emptyMap();
        }
        List<AptBillOperationContent> contents = operationService.queryOperationContents(opIds);
        return contents.stream()
                       .collect(Collectors.groupingBy(AptBillOperationContent::getOperationId));
    }

}

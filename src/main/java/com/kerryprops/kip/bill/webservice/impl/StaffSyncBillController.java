package com.kerryprops.kip.bill.webservice.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.aop.RedisLock;
import com.kerryprops.kip.bill.common.constants.BillConstants;
import com.kerryprops.kip.bill.common.enums.SendBillStatus;
import com.kerryprops.kip.bill.common.utils.BuConverter;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.utils.jde.OracleJdbc;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.config.DataMigrationConfig;
import com.kerryprops.kip.bill.config.SyncJdeConfig;
import com.kerryprops.kip.bill.dao.BillEmailTraceRepository;
import com.kerryprops.kip.bill.dao.BillRepository;
import com.kerryprops.kip.bill.dao.entity.BillEmailTrace;
import com.kerryprops.kip.bill.dao.entity.BillEntity;
import com.kerryprops.kip.bill.dao.entity.QBillEmailTrace;
import com.kerryprops.kip.bill.dao.entity.QBillEntity;
import com.kerryprops.kip.bill.feign.clients.FileClient;
import com.kerryprops.kip.bill.feign.clients.HiveAsClient;
import com.kerryprops.kip.bill.feign.clients.KlClient;
import com.kerryprops.kip.bill.feign.clients.MessageClient;
import com.kerryprops.kip.bill.feign.entity.EmailSendCommand;
import com.kerryprops.kip.bill.feign.entity.UploadPublicFileResponse;
import com.kerryprops.kip.bill.service.IBillConfigService;
import com.kerryprops.kip.bill.service.IBillService;
import com.kerryprops.kip.bill.service.model.leg.Bill;
import com.kerryprops.kip.bill.webservice.StaffSyncBillFacade;
import com.kerryprops.kip.bill.webservice.vo.req.EmailResultVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffBillReceiverRespVo;
import com.kerryprops.kip.hiveas.webservice.vo.resp.ProjectBuildingVO;
import com.querydsl.core.types.dsl.BooleanExpression;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.ALERT_MAIL_RECEIVERS;

/***********************************************************************************************************************
 * Project - accelerator
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/03/2021 14:05
 **********************************************************************************************************************/

@Slf4j
@RestController
public class StaffSyncBillController implements StaffSyncBillFacade {

    private static final String K_BILL = "kb:";

    private static final String DRIVER = "com.mysql.jdbc.Driver";

    public static AtomicInteger totalRecord = new AtomicInteger(0);

    public static AtomicInteger processedRecord = new AtomicInteger(0);

    @Autowired
    private IBillService billService;

    @Autowired
    private FileClient fileClient;

    @Autowired
    private HiveAsClient hiveAsClient;

    @Autowired
    private MessageClient messageClient;

    @Autowired
    private BillEmailTraceRepository emailTraceRepository;

    @Autowired
    private IBillConfigService billConfigService;

    @Autowired
    private BillRepository billRepository;

    @Autowired
    private KlClient klClient;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Value("${env}")
    private String env;

    @Autowired
    private EntityManager em;

    @Autowired
    private DataMigrationConfig dataMigrationConfig;

/*    @Override
    @Async
    public RespWrapVo<String> syncKlCheck(@RequestParam(value = "projectId", required = false) List<String> projectIds,
                                          @RequestParam(value = "year", required = false) Integer year,
                                          @RequestParam(value = "month", required = false) Integer month,
                                          @RequestParam(value = "receivers", required = false) List<String> receivers,
                                          @RequestParam(value = "auth", required = false) String auth) {
        if (CollectionUtils.isEmpty(receivers)) {
            return null;
        }
        UserInfoUtils.setUser("{\"nickName\":\"SuperAdmin\",\"roles\":\"SUPER_ADMIN\",\"userId\":123,\"phoneNumber\":\"13661600321\",\"fromType\":\"S\"}");
        Calendar calendar = Calendar.getInstance();
        if (year == null) {
            year = 21;
        }
        if (month == null) {
            month = 11;
        }
        Integer pageSize = null;
        if (pageSize == null) {
            pageSize = 1000000;
        }
        List<String> buList = getBuList(projectIds);
        String klBus = null;
        if (CollectionUtils.isNotEmpty(buList)) {
            klBus = StringUtils.join(buList, ",");
        }
        JSONObject billJson = klClient.list(1, pageSize, year, month, klBus, auth);
        JSONArray billArr = billJson.getJSONArray("rows");
        if (billArr == null) {
            return null;
        }
        Map<Integer, Set<String>> sourceEmails = new HashMap<>();
        for (int y = 0; y < billArr.size(); y++) {
            JSONObject b = billArr.getJSONObject(y);
            Integer id = b.getInteger("id");
            sourceEmails.put(id, new HashSet<>());
            JSONArray us = b.getJSONArray("userInfoList");
            if (us != null) {
                for (int z = 0; z < us.size(); z++) {
                    JSONObject c = us.getJSONObject(z);
                    if (StringUtils.isNotEmpty(c.getString("email"))) {
                        String email = c.getString("email");
                        String[] argg = StringUtils.split(email, ",");
                        for (String s : argg) {
                            if (StringUtils.isNotEmpty(s) || StringUtils.isNotEmpty(s.trim())) {
                                sourceEmails.get(id).add(s.trim());
                            }
                        }
                    }
                    if (StringUtils.isNotEmpty(c.getString("emailCc"))) {
                        String email = c.getString("emailCc");
                        String[] argg = StringUtils.split(email, ",");
                        for (String s : argg) {
                            if (StringUtils.isNotEmpty(s) || StringUtils.isNotEmpty(s.trim())) {
                                sourceEmails.get(id).add(s.trim());
                            }
                        }
                    }
                }
            }
        }

        BillSearchReqVo billSearchReqVo = new BillSearchReqVo();
        billSearchReqVo.setTpFyr(year);
        billSearchReqVo.setTpPn(month);
        billSearchReqVo.setBuildingIds(getBuildingList(projectIds));

        BillSearchReqBo billSearchReqBo = BeanUtil.copy(billSearchReqVo, BillSearchReqBo.class);
        populateDataFields(billSearchReqBo, billSearchReqVo.getBuildingIds());

        List<Bill> bills = billService.selectBillVoList(billSearchReqBo.toPredicates());
//        Page<BillSelectVo> list = billService.selectBillVoList(pageable, billSearchReqBo.toPredicates());
        List<StaffBillRespVo> staffBillRespVoList = bills.stream()
                .map(e -> BeanUtil.copy(e, StaffBillRespVo.class))
                .collect(Collectors.toList());
        Map<DocoAn8, List<StaffBillRespVo>> an8Map = new HashMap<>();
        for (StaffBillRespVo aBill : staffBillRespVoList) {
            String doco = aBill.getTpDoco();
            if (StringUtils.isEmpty(doco)) {
                aBill.setCheckedFlag(false);
                continue;
            }
            String an8 = aBill.getTpAn8();
            if (StringUtils.isEmpty(an8)) {
                aBill.setCheckedFlag(false);
                continue;
            }
            DocoAn8 key = DocoAn8.builder().doco(doco).an8(an8).build();
            if (!an8Map.containsKey(key)) {
                an8Map.put(key, new ArrayList<>());
            }
            an8Map.get(key).add(aBill);
        }
        Map<DocoAn8, TenantRespDto> tenantCache = new HashMap<>();
        Map<DocoAn8, Set<StaffBillReceiverRespVo>> ss = billService.selectBillReceiverByDoco2(an8Map, tenantCache);
        if (ss != null && !ss.isEmpty()) {
            for (Map.Entry<DocoAn8, List<StaffBillRespVo>> docoAn8ListEntry : an8Map.entrySet()) {
                DocoAn8 docoAn8 = docoAn8ListEntry.getKey();
                List<StaffBillRespVo> staffBillRespVos = docoAn8ListEntry.getValue();
                Set<StaffBillReceiverRespVo> userInfoList = ss.get(docoAn8);
                if (CollectionUtils.isNotEmpty(userInfoList)) {
                    for (StaffBillRespVo staffBillRespVo : staffBillRespVos) {
                        staffBillRespVo.setCheckedFlag(true);
                        staffBillRespVo.setUserInfoList(userInfoList);
                    }
                }
            }
        }

        Map<String, List<StaffBillRespVo>> result = new HashMap<>();
        List<String> tags = Lists.newArrayList(
                "都有且匹配",
                "都有但Kerry+多",
                "都有但Kerry+少",
                "Kerry+有但KL没有",
                "Kerry+没有但KL有",
                "Kerry+和KL都没有",
                "人工核验");
        for (String tag : tags) {
            result.put(tag, new ArrayList<>());
        }

        for (StaffBillRespVo staffBillRespVo : staffBillRespVoList) {
            Integer sourceId = staffBillRespVo.getSourceId();

            Set<StaffBillReceiverRespVo> currentReceivers = staffBillRespVo.getUserInfoList();
            Set<String> klEmails = sourceEmails.get(sourceId);

            if (CollectionUtils.isNotEmpty(currentReceivers) && CollectionUtils.isNotEmpty(klEmails)) {
                Set<String> kEmails = currentReceivers.stream().map(e -> e.getEmail()).collect(Collectors.toSet());
                if (kEmails.size() == klEmails.size()) {
                    if (klEmails.stream().allMatch(e -> kEmails.contains(e))) {
                        result.get(tags.get(0)).add(staffBillRespVo);
                    } else {
                        result.get(tags.get(6)).add(staffBillRespVo);
                    }
                } else if (kEmails.size() > klEmails.size()) {
                    result.get(tags.get(1)).add(staffBillRespVo);
                } else {
                    result.get(tags.get(2)).add(staffBillRespVo);
                }
            } else if (CollectionUtils.isEmpty(currentReceivers) && CollectionUtils.isNotEmpty(klEmails)) {
                result.get(tags.get(4)).add(staffBillRespVo);
            } else if (CollectionUtils.isNotEmpty(currentReceivers) && CollectionUtils.isEmpty(klEmails)) {
                result.get(tags.get(3)).add(staffBillRespVo);
            } else {
                result.get(tags.get(5)).add(staffBillRespVo);
            }
        }

        StringBuffer sb = new StringBuffer("\n账单接收人匹配检查结果: \n");
        List<ProjectBuildingVO> centerRespVos = getProjectLists(projectIds);
        if (CollectionUtils.isNotEmpty(centerRespVos)) {
            sb.append("检查范围:").append(
                    Arrays.toString(centerRespVos
                            .stream()
                            .map(ProjectBuildingVO::getProject)
                            .filter(Objects::nonNull)
                            .map(e -> e.getId() + "-" + e.getName()).toArray()
                    )
            ).append("\n");
        }
        sb.append("账单周期:").append(year).append("-").append(month).append("\n");
        sb.append("检查时间:").append(DateUtils.getTime()).append("\n\n");
        int index = 0;
        for (int z = 1; z < tags.size(); z++) {
            String tag = tags.get(z);
            index++;

            List<StaffBillRespVo> tmp = result.get(tag);
            sb.append(index).append(". ").append(tag).append(":").append("(总量：").append(tmp.size()).append(")\n");
            int xx = 1;
            Map<String, Map<String, List<String>>> checker = new HashMap<>();
            for (StaffBillRespVo staffBillRespVo : tmp) {
                String kEmailStr = "";

                Set<StaffBillReceiverRespVo> currentReceivers = staffBillRespVo.getUserInfoList();
                if (CollectionUtils.isNotEmpty(currentReceivers)) {
                    Set<String> kEmails = currentReceivers.stream().map(e -> e.getEmail()).collect(Collectors.toSet());
                    kEmailStr = Arrays.toString(kEmails.toArray());
                }

                String klEmailStr = "";
                Set<String> klEmails = sourceEmails.get(staffBillRespVo.getSourceId());
                Set<String> tmpKlEmails = new HashSet<>();
                if (CollectionUtils.isNotEmpty(klEmails)) {
                    for (String klEmail : klEmails) {
                        if (StringUtils.isEmpty(klEmail)) {
                            continue;
                        }
                        tmpKlEmails.addAll(Arrays.stream(StringUtils.split(klEmail, ","))
                                .map(e -> StringUtils.trim(e))
                                .filter(e -> StringUtils.isNotEmpty(e))
                                .collect(Collectors.toList()));
                    }
                    klEmailStr = Arrays.toString(tmpKlEmails.toArray());
                }
                String doco = staffBillRespVo.getTpDoco();
                if (!checker.containsKey(doco)) {
                    checker.put(doco, new HashMap<>());
                }

                Map<String, List<String>> docoMap = checker.get(doco);
                if (!docoMap.containsKey(kEmailStr)) {
                    docoMap.put(kEmailStr, new ArrayList<>());
                }

                if (docoMap.get(kEmailStr).contains(klEmailStr)) {
                    continue;
                } else {
                    docoMap.get(kEmailStr).add(klEmailStr);
                }

                sb.append("  ").append(index).append(".").append(xx++).append(". ")
                        .append("账单号：").append(staffBillRespVo.getId()).append(", 账单合同号：").append(staffBillRespVo.getTpDoco());
                TenantRespDto tenantRespDto = tenantCache.get(DocoAn8.builder().doco(staffBillRespVo.getTpDoco()).an8(staffBillRespVo.getTpAn8()).build());
                if (tenantRespDto == null) {
                    sb.append(", 租户ID：").append("没有租户");
                } else {
                    sb.append(", 租户ID：").append(tenantRespDto.getId())
                            .append(", 租户名：").append(tenantRespDto.getBrandName())
                            .append(", 租户所有合同号：").append(Arrays.toString(tenantRespDto.getDoCoSet().toArray()));
                }
                sb.append("\n");
                sb.append("    KerryPlus Emails:").append(kEmailStr).append("\n");
                sb.append("    KerryLink Emails:").append(klEmailStr).append("\n\n");
            }
            sb.append("\n");
        }
        log.info(sb.toString());
        EmailSendCommand command = new EmailSendCommand();
        command.setSendTos(receivers);
        command.setSubject("账单接收人核验报告(账期" + year + "-" + month + ")");
        command.setText(sb.toString());
        messageClient.sendWithReply(command);
        return new RespWrapVo<>(sb.toString());
    }*/

    @Override
    @RedisLock(key = "kip:billing:b:syncjdebill", expire = 30L)
    public RespWrapVo<Boolean> sync() {
        log.info("办公商场：同步JDE账单开始");

        String buString = getJdeBus(null);
        if (StringUtils.isEmpty(buString)) {
            log.warn("未找到有效的BU信息，停止同步");
            return new RespWrapVo<>(false);
        }

        Map<String, String> billSource = billConfigService.getBillConfigList();
        if (billSource == null || billSource.isEmpty()) {
            throw new RuntimeException("账单读取来源没有配置");
        }
        log.info("获取账单读取来源配置完成");

        String sqlSuffix = " FROM " + SyncJdeConfig.getBillTable()
                + " WHERE TPMCU in (" + buString + ") AND TPEV01 ='Y' AND TPEV03 != 'Y' ORDER BY TPCRTUTIME";

        String countSql = "select count(*) as cnt " + sqlSuffix;
        log.info("查询账单总数，SQL: {}", countSql);
        Integer totalCnt = null;
        try (Connection conn = OracleJdbc.getOracleConnection();
             Statement statement = conn.createStatement();
             ResultSet rs = statement.executeQuery(countSql)
        ) {
            if (rs != null && rs.next()) {
                totalCnt = rs.getInt("cnt");
            }
        } catch (Exception e) {
            log.error("查询账单总数失败", e);
            return new RespWrapVo<>(false);
        }

        if (totalCnt == null || totalCnt == 0) {
            log.info("没有需要同步的JDE账单，同步终止");
            return new RespWrapVo<>(true);
        }
        log.info("需要同步的账单总数: {}", totalCnt);

        List<Bill> billList = new LinkedList<>();
        String selectSql = "SELECT * " + sqlSuffix;
        log.info("查询账单数据，SQL: {}", selectSql);
        try (Connection conn = OracleJdbc.getOracleConnection();
             Statement statement = conn.createStatement();
             ResultSet rs = statement.executeQuery(selectSql)
        ) {
            int updateCnt = 0;
            int insertCnt = 0;
            int processedCnt = 0;
            while (rs != null && rs.next()) {
                processedCnt++;
                Bill bill = null;
                try {
                    String tpCo = StringUtils.trim(rs.getString("TPCO"));
                    String tpDoco = StringUtils.trim(rs.getString("TPDOCO"));
                    String tpDct = StringUtils.trim(rs.getString("TPDCT"));
                    int tpFyr = rs.getInt("TPFYR");
                    int tpPn = rs.getInt("TPPN");
                    String tpDl03 = StringUtils.trim(rs.getString("TPDL03"));
                    String tpAn8 = StringUtils.trim(rs.getString("TPAN8"));
                    String tpAlph = StringUtils.trim(rs.getString("TPALPH"));
                    String tpDl01 = StringUtils.trim(rs.getString("TPDL01"));
                    String tpGtfilenm = StringUtils.trim(rs.getString("TPGTFILENM"));
                    String tpGtitnm = StringUtils.trim(rs.getString("TPGTITNM"));
                    String tpUnit = StringUtils.trim(rs.getString("TPUNIT"));
                    String tpMcu = StringUtils.trim(rs.getString("TPMCU"));
                    String tpDc = StringUtils.trim(rs.getString("TPDC"));
                    String tpEv01 = StringUtils.trim(rs.getString("TPEV01"));
                    String tpEv02 = StringUtils.trim(rs.getString("TPEV02"));
                    Date tpCrtutime = rs.getTimestamp("TPCRTUTIME");
                    String formatDate = "";
                    String tpk74dpd = StringUtils.trim(rs.getString("TPK74DPD"));

                    if (tpk74dpd.length() == 6) {
                        formatDate = DateUtils.convertDateYYYYMMDD(tpk74dpd);
                    }

                    bill = new Bill(tpDct, tpDl01, tpGtfilenm, tpCo, tpDl03, tpAn8, tpAlph, tpDoco,
                            tpMcu, tpDc, tpCrtutime, tpFyr, tpPn, tpEv01, tpEv02, formatDate, tpGtitnm, tpUnit);
                    bill.setBillMonth(tpFyr * 100 + tpPn);
                    int result = saveBill(bill, billSource);
                    if (result == 1) {
                        insertCnt++;
                    } else if (result == 2) {
                        updateCnt++;
                    }
                    if (result != 0) {
                        billList.add(bill);
                    }
                    log.info("sync jde bill progress: processedCnt/totalCnt={}/{}, insertCnt/totalCnt={}/{}, updateCnt/totalCnt={}/{} - {}", processedCnt, totalCnt, insertCnt, totalCnt, updateCnt, totalCnt, "success");
                } catch (Exception e) {
                    EmailSendCommand command = new EmailSendCommand();
                    command.setSubject(env + "sync bill from jde failed: Save bill failed");
                    command.setSendTos(ALERT_MAIL_RECEIVERS);
                    String billString = "";
                    if (bill != null) {
                        billString = bill.toString();
                    }
                    command.setText(ExceptionUtils.getStackTrace(e) + "\r" + billString);
                    messageClient.sendWithReplyAlicloud(command);
                    log.error("Save jde bill failed.", e);
                    log.info("sync jde bill progress: processedCnt/totalCnt={}/{} - {}", processedCnt, totalCnt, "failed");
                }
            }
        } catch (Exception e) {
            EmailSendCommand command = new EmailSendCommand();
            command.setSubject(env + "sync bill from jde failed.");
            command.setSendTos(ALERT_MAIL_RECEIVERS);
            command.setText(ExceptionUtils.getStackTrace(e));
            messageClient.sendWithReplyAlicloud(command);
            log.error("sync bill from jde failed.", e);
        }

        billService.writeBackJDESyncFlag(billList);

        log.info("办公商场：同步JDE账单完成");
        return new RespWrapVo<>(true);
    }

    @Override
    public RespWrapVo<Boolean> sync(@RequestParam(value = "type", required = true) Integer type,
                                    @RequestParam(value = "dt", required = true) String dt) {
        String buString = getJdeBusManu(type);
        if (StringUtils.isEmpty(buString)) {
            return new RespWrapVo<>(false);
        }

        Map<String, String> billSource = billConfigService.getBillConfigList();
        if (billSource == null || billSource.isEmpty()) {
            throw new RuntimeException("账单读取来源没有配置");
        }

        String sqlSuffix = null;
        String maxDateStr = StringUtils.isNotEmpty(dt) ? dt.trim() : StringUtils.EMPTY;
        if (StringUtils.isNotEmpty(maxDateStr)) {
            sqlSuffix = " FROM " + SyncJdeConfig.getBillTable()
                    + " WHERE TPMCU in (" + buString + ") and TPEV01 ='Y' AND TPEV03 != 'Y' AND TPCRTUTIME > to_date('" + maxDateStr
                    + "','yyyy-mm-dd hh24:mi:ss') ORDER BY TPCRTUTIME";
        } else {
            sqlSuffix = " FROM " + SyncJdeConfig.getBillTable()
                    + " WHERE TPMCU in (" + buString + ") and TPEV01 ='Y' AND TPEV03 != 'Y' ORDER BY TPCRTUTIME";
        }

        String countSql = "select count(*) as cnt " + sqlSuffix;
        Integer totalCnt = null;
        try (Connection conn = OracleJdbc.getOracleConnection();
             Statement statement = conn.createStatement();
             ResultSet rs = statement.executeQuery(countSql)
        ) {
            if (rs != null && rs.next()) {
                totalCnt = rs.getInt("cnt");
            }
        } catch (Exception e) {
            log.error("get jde bill count failed.", e);
            return new RespWrapVo<>(false);
        }

        if (totalCnt == null || totalCnt == 0) {
            log.info("no more jde bill to be synced");
            return new RespWrapVo<>(true);
        }
        log.info("total size from jde bill need to be synced: {}", totalCnt);

        List<Bill> billList = new LinkedList<>();
        String selectSql = "SELECT * " + sqlSuffix;
        log.info("sync b bill sql: {}", selectSql);
        try (Connection conn = OracleJdbc.getOracleConnection();
             Statement statement = conn.createStatement();
             ResultSet rs = statement.executeQuery(selectSql)
        ) {
            int updateCnt = 0;
            int insertCnt = 0;
            int processedCnt = 0;
            while (rs != null && rs.next()) {
                processedCnt++;
                Bill bill = null;
                try {
                    String tpCo = StringUtils.trim(rs.getString("TPCO"));
                    String tpDoco = StringUtils.trim(rs.getString("TPDOCO"));
                    String tpDct = StringUtils.trim(rs.getString("TPDCT"));
                    int tpFyr = rs.getInt("TPFYR");
                    int tpPn = rs.getInt("TPPN");
                    String tpDl03 = StringUtils.trim(rs.getString("TPDL03"));
                    String tpAn8 = StringUtils.trim(rs.getString("TPAN8"));
                    String tpAlph = StringUtils.trim(rs.getString("TPALPH"));
                    String tpDl01 = StringUtils.trim(rs.getString("TPDL01"));
                    String tpGtfilenm = StringUtils.trim(rs.getString("TPGTFILENM"));
                    String tpGtitnm = StringUtils.trim(rs.getString("TPGTITNM"));
                    String tpUnit = StringUtils.trim(rs.getString("TPUNIT"));
                    String tpMcu = StringUtils.trim(rs.getString("TPMCU"));
                    String tpDc = StringUtils.trim(rs.getString("TPDC"));
                    String tpEv01 = StringUtils.trim(rs.getString("TPEV01"));
                    String tpEv02 = StringUtils.trim(rs.getString("TPEV02"));
                    Date tpCrtutime = rs.getTimestamp("TPCRTUTIME");
                    String formatDate = "";
                    String tpk74dpd = StringUtils.trim(rs.getString("TPK74DPD"));
                    if (tpk74dpd.length() == 6) {
                        formatDate = DateUtils.convertDateYYYYMMDD(tpk74dpd);
                    }
                    bill = new Bill(tpDct, tpDl01, tpGtfilenm, tpCo, tpDl03, tpAn8, tpAlph, tpDoco,
                            tpMcu, tpDc, tpCrtutime, tpFyr, tpPn, tpEv01, tpEv02, formatDate, tpGtitnm, tpUnit);
                    bill.setBillMonth(tpFyr * 100 + tpPn);
                    int result = saveBill(bill, billSource);
                    if (result == 1) {
                        insertCnt++;
                    } else if (result == 2) {
                        updateCnt++;
                    }
                    if (result != 0) {
                        billList.add(bill);
                    }
                    log.info("sync jde bill progress: processedCnt/totalCnt={}/{}, insertCnt/totalCnt={}/{}, updateCnt/totalCnt={}/{} - {}", processedCnt, totalCnt, insertCnt, totalCnt, updateCnt, totalCnt, "success");
                } catch (Exception e) {
                    EmailSendCommand command = new EmailSendCommand();
                    command.setSubject(env + "sync bill from jde failed: Save bill failed");
                    command.setSendTos(ALERT_MAIL_RECEIVERS);
                    String billString = "";
                    if (bill != null) {
                        billString = bill.toString();
                    }
                    command.setText(ExceptionUtils.getStackTrace(e) + "\r" + billString);
                    messageClient.sendWithReplyAlicloud(command);
                    log.error("Save jde bill failed.", e);
                    log.info("sync jde bill progress: processedCnt/totalCnt={}/{} - {}", processedCnt, totalCnt, "failed");
                }
            }
        } catch (Exception e) {
            EmailSendCommand command = new EmailSendCommand();
            command.setSubject(env + "sync bill from jde failed.");
            command.setSendTos(ALERT_MAIL_RECEIVERS);
            command.setText(ExceptionUtils.getStackTrace(e));
            messageClient.sendWithReplyAlicloud(command);
            log.error("sync bill from jde failed.", e);
        }

        billService.writeBackJDESyncFlag(billList);

        return new RespWrapVo<>(true);
    }

    /**
     * 这里进行 账单的文件下载以及保存到数据库
     *
     * @param billList
     * @param billSource 账单文件来源的服务
     */
/*    public void downloadBillFileAndInsert(List<Bill> billList, Map<String, String> billSource) {
        List<BillLog> billLogList = new ArrayList<>();
        for (Bill bill : billList) {
            String billHttpService = "";
            if (StringUtils.isBlank(bill.getTpEv02())) {
                billHttpService = billSource.get("DZ");
            } else {
                billHttpService = billSource.get(bill.getTpEv02());
            }
            String fileUrl = billHttpService + bill.getTpGtfilenm();
            String newFileUrl = uploadFileToOss(fileUrl);
            if (StringUtils.isEmpty(newFileUrl)) {//账单文件缺失
                bill.setTpStatus(20);
            } else {
                bill.setTpStatus(0);
                bill.setFileUrl(newFileUrl);
            }

            BillLog billLog = new BillLog();
            BeanUtils.copyProperties(bill, billLog);
            billLog.setCreateTime(DateUtils.getNowDate());
            billLogList.add(billLog);
            List<BillEntity> oldBill = billService.selectOldBill(bill);
            billService.insertOrUpdateNewBill(oldBill, bill);
        }
        if (billLogList.size() > 0) {
            int size = billLogList.size();
            List<BillLog> batchBillLogs = new ArrayList<>();
            for (int i = 0; i < size; i++) {
                batchBillLogs.add(billLogList.get(i));
                if (i % 500 == 0) {
                    billService.batchBillLogList(batchBillLogs);
                    batchBillLogs.clear();
                }
            }
            if (batchBillLogs.size() > 0) {
                billService.batchBillLogList(batchBillLogs);
            }
        }
    }*/
    @Override
    @RedisLock(key = "kip:billing:b:syncEmailStatus", expire = 60L)
    public RespWrapVo<Boolean> syncEmailStatus(@RequestParam(value = "requestIds", required = false) List<String> requestIds,
                                               @RequestParam(value = "autoFix", required = false) Boolean autoFix) {
        if (CollectionUtils.isEmpty(requestIds)) {
            BooleanExpression expression = QBillEmailTrace.billEmailTrace.status.eq(0);
            if (autoFix != null && autoFix) {
                Calendar c = Calendar.getInstance();
                c.add(Calendar.MINUTE, -30);
                expression = expression.and(QBillEmailTrace.billEmailTrace.createTime.lt(c.getTime()));
            }
            Iterable<BillEmailTrace> emailTraces = emailTraceRepository.findAll(expression);
            if (emailTraces != null) {
                List<BillEmailTrace> list = Lists.newArrayList(emailTraces.iterator());
                requestIds = list.stream().map(BillEmailTrace::getRequestId).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(requestIds)) {
            return new RespWrapVo<>(false);
        }
        for (String requestId : requestIds) {
            try {
                RespWrapVo<EmailResultVo> emailResultVoRespWrapVo = messageClient.getSendStatus(requestId);
                EmailResultVo vo = emailResultVoRespWrapVo.getData();
                log.info("request {} email status: {}", requestId, vo);
                billService.emailCallBack(emailResultVoRespWrapVo.getData());
            } catch (Exception e) {
                log.error("fetch email status failed: " + requestId, e);
            }
        }
        return new RespWrapVo<>(true);
    }

    @Override
    @RedisLock(key = "kip:billing:b:syncBillEmailStatus", expire = 30L)
    public RespWrapVo<Boolean> syncBillEmailStatus(@RequestParam(value = "billId", required = false) Long billId) {
        Iterable<BillEntity> billEntityIterable;
        if (billId == null) {
            Calendar c = Calendar.getInstance();
            c.add(Calendar.MINUTE, -16);
            billEntityIterable = billRepository.findAll(QBillEntity.billEntity.emailStatus.eq(3).and(QBillEntity.billEntity.emailDate.lt(c.getTime())));
        } else {
            billEntityIterable = billRepository.findAll(QBillEntity.billEntity.id.eq(billId));
        }
        if (billEntityIterable == null || billEntityIterable.iterator() == null || !billEntityIterable.iterator().hasNext()) {
            log.info("No more sending status bill");
            return new RespWrapVo<>(true);
        }
        List<BillEntity> billEntities = Lists.newArrayList(billEntityIterable.iterator());
        for (BillEntity billEntity : billEntities) {
            String emailErr = billEntity.getEmailErr();
            if (StringUtils.isEmpty(emailErr)) {
                billRepository.updateEmailStatus(billEntity.getId(), BillConstants.MSG_NOT_SEND, BillConstants.MSG_NOT_SEND);
                log.info("revert bill email send status to {}, bill: {}", BillConstants.MSG_NOT_SEND, billEntity.getId());
                continue;
            }

            List<StaffBillReceiverRespVo> userInfoList = new ArrayList<>(JSONObject.parseArray(emailErr, StaffBillReceiverRespVo.class));
            List<String> pendingRequestIds = userInfoList
                    .stream()
                    .filter(e -> StringUtils.isNotEmpty(e.getRequestId()) && StringUtils.equalsIgnoreCase(e.getSendStatus(), SendBillStatus.SENDING.getMessage()))
                    .map(e -> e.getRequestId())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pendingRequestIds)) {
                int successCnt = 0;
                int failedCnt = 0;
                int totalCnt = 0;
                for (StaffBillReceiverRespVo e : userInfoList) {
//                     if (StringUtils.isEmpty(e.getRequestId())) {
//                         continue;
//                     }
                    totalCnt++;
                    if (StringUtils.equalsIgnoreCase(e.getSendStatus(), SendBillStatus.SEND_SUCCESS.getMessage())) {
                        successCnt++;
                    } else {
                        failedCnt++;
                    }
                }
                int tc;
                if (totalCnt == successCnt) {
                    tc = BillConstants.MSG_SUCCESS;
                } else if (totalCnt == failedCnt) {
                    tc = BillConstants.MSG_FAILURE;
                } else {
                    tc = BillConstants.MSG_PARTIAL_SUCCESS;
                }
                billRepository.updateEmailStatus(billEntity.getId(), tc);
                log.info("renew bill email send status to {}, bill: {}", tc, billEntity.getId());
                continue;
            }

            for (String requestId : pendingRequestIds) {
                try {
                    RespWrapVo<EmailResultVo> emailResultVoRespWrapVo = messageClient.getSendStatus(requestId);
                    EmailResultVo vo = emailResultVoRespWrapVo.getData();
                    log.info("request {} email status: {}", requestId, vo);
                    billService.emailCallBack(emailResultVoRespWrapVo.getData());
                } catch (Exception e) {
                    log.error("fetch email status failed: " + requestId, e);
                }
            }
        }

        return new RespWrapVo<>(true);
    }

    /**
     * 接口已废弃，功能暂不使用
     */
/*    @Override
    @RedisLock(key = "kip:billing:b:synckl", expire = 60L)
    public RespWrapVo<String> syncKl(@RequestParam(value = "projectId", required = false) List<String> projectIds,
                                     @RequestParam(value = "deleteAll", required = false) Boolean deleteAll,
                                     @RequestParam(value = "year", required = false) String year) {

        totalRecord.set(0);
        processedRecord.set(0);

        String buString = getBus(projectIds);
        if (StringUtils.isEmpty(buString)) {
            return new RespWrapVo<>("bu not found.");
        }
        if (deleteAll != null && deleteAll) {
            billRepository.deleteAll();
        }

        String cntSql = "select count(1) as cnt from kerry_bill where tp_mcu in (%s) and tp_fyr " + year;
        cntSql = String.format(cntSql, buString);
        try (Connection connection = getJdbcCon();
             PreparedStatement ps = connection.prepareStatement(cntSql);
             ResultSet rs = ps.executeQuery()
        ) {
            while (rs.next()) {
                totalRecord.set(rs.getInt("cnt"));
            }
        } catch (Exception e) {
            log.error("connect kl failed.", e);
        }
        log.info("total Record: {}", totalRecord.get());
        String sql = "select * from kerry_bill where tp_mcu in (%s) and tp_fyr " + year + " and tp_pn=%s";
        for (int i = 1; i < 13; i++) {
            String sql1 = String.format(sql, buString, i);
            String tc = new String(String.valueOf(i));
            log.info("sync kl sql: {}", sql1);
            new Thread(new Runnable() {

                @Override
                public void run() {
                    synckl(sql1, tc);
                }
            }).start();
        }
        return new RespWrapVo<>("true");
    }*/

/*    public void synckl(String sql, String index) {
        int total = 0;
        int success = 0;
        try (Connection connection = getJdbcCon();
             PreparedStatement ps = connection.prepareStatement(sql);
             ResultSet rs = ps.executeQuery()
        ) {
            while (rs.next()) {
                int p = processedRecord.incrementAndGet();
                log.info("sync kl record progress {}: {}/{}", index, p, totalRecord.get());
                total++;
                BillEntity bill = null;
                try {
                    Integer year = rs.getInt("tp_fyr");
                    Integer month = rs.getInt("tp_pn");
                    Integer billMonth = null;
                    if (year != null && month != null) {
                        billMonth = year * 100 + month;
                    }
                    String fileUrl = rs.getString("file_url");
                    String newFileUrl = null;
                    if (StringUtils.isNotEmpty(fileUrl) && StringUtils.startsWith(fileUrl, "https://kerrylinks.kerryprops.com.cn")) {
                        newFileUrl = uploadFileToOss(fileUrl);
                    }
                    String originalEmailErr = rs.getString("email_err");
                    Set<StaffBillReceiverRespVo> originalUserInfoList = new HashSet<>();
                    Date emailDate = null;
                    if (StringUtils.isNotEmpty(originalEmailErr)) {
                        try {
                            JSONArray gsonArray = JSONObject.parseArray(originalEmailErr);
                            for (int x = 0; x < gsonArray.size(); x++) {
                                JSONObject gson = gsonArray.getJSONObject(x);
                                String failureInfo = gson.getString("failureInfo");
                                String errorMsg = gson.getString("errInfo");
                                String sendTime = gson.getString("sendDate");
                                try {
                                    Date sendDate = DateUtils.parseDate(sendTime, DateUtils.YYYY_MM_DD_HH_MM_SS);
                                    if (emailDate == null || emailDate.getTime() < sendDate.getTime()) {
                                        emailDate = sendDate;
                                    }
                                } catch (Exception e) {
                                    //ignore
                                }
                                if (StringUtils.isNotEmpty(failureInfo)) {
                                    try {
                                        for (String s : failureInfo.split(",")) {
                                            String status = SendBillStatus.SEND_FAIL.getMessage();
                                            if (StringUtils.isNotEmpty(errorMsg)) {
                                                status += ":" + errorMsg;
                                            }
                                            originalUserInfoList.add(StaffBillReceiverRespVo.builder()
                                                    .requestId(gson.getString("batchCode"))
                                                    .email(s)
                                                    .sendStatus(status)
                                                    .sendTime(sendTime)
                                                    .build());
                                        }
                                    } catch (Exception e) {
                                        log.error("process failureInfo  failed." + failureInfo, e);
                                    }
                                }
                                String successInfo = gson.getString("successInfo");
                                if (StringUtils.isNotEmpty(successInfo)) {
                                    try {
                                        for (String s : successInfo.split(",")) {
                                            originalUserInfoList.add(StaffBillReceiverRespVo.builder()
                                                    .requestId(gson.getString("batchCode"))
                                                    .email(s)
                                                    .sendStatus(SendBillStatus.SEND_SUCCESS.getMessage())
                                                    .sendTime(sendTime)
                                                    .build());
                                        }
                                    } catch (Exception e) {
                                        log.error("process successInfo failed." + successInfo, e);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            log.error("parse email error failed. " + originalEmailErr, e);
                            originalUserInfoList.add(StaffBillReceiverRespVo.builder()
                                    .sendStatus(originalEmailErr)
                                    .build());
                        }
                    }
                    String emailError = null;
                    if (CollectionUtils.isNotEmpty(originalUserInfoList)) {
                        emailError = JSONObject.toJSONString(originalUserInfoList);
                    }
                    Integer sourceId = rs.getInt("id");
//                    Iterable<BillEntity> billEntities = billRepository.findAll(QBillEntity.billEntity.sourceId.eq(sourceId));
//                    if(billEntities != null && billEntities.iterator().hasNext()){
//                        billRepository.deleteAll(billEntities);
//                        log.info("bill {} exist, ignore.", sourceId);
//                        continue;
//                    }
                    if (emailDate == null) {
                        emailDate = DateUtils.convertTimestampIntoDate(rs.getTimestamp("email_date"));
                    }

                    bill = BillEntity.builder()
                            .tpDct(rs.getString("tp_dct"))
                            .tpDl01(rs.getString("tp_dl01"))
                            .fileUrl(newFileUrl)
                            .tpGtfilenm(rs.getString("tp_gtfilenm"))
                            .tpCo(rs.getString("tp_co"))
                            .tpDl03(rs.getString("tp_dl03"))
                            .tpAn8(rs.getString("tp_an8"))
                            .tpAlph(rs.getString("tp_alph"))
                            .tpDoco(rs.getString("tp_doco"))
                            .tpMcu(rs.getString("tp_mcu"))
                            .tpDc(rs.getString("tp_dc"))
                            .tpCrtutime(DateUtils.convertTimestampIntoDate(rs.getTimestamp("tp_crtutime")))
                            .tpStatus(rs.getInt("tp_status"))
                            .tpFyr(year)
                            .tpPn(month)
                            .billMonth(billMonth)
                            .tpEv01(rs.getString("tp_ev01"))
                            .tpEv02(rs.getString("tp_ev02"))
                            .delFlag(rs.getString("del_flag"))
                            .formatDate(rs.getString("format_date"))
                            .tpGtitnm(rs.getString("tp_gtitnm"))
                            .tpUnit(rs.getString("tp_unit"))
                            .updateBy(rs.getString("update_by"))
                            .mailStatus(rs.getInt("mail_status"))
                            .emailStatus(rs.getInt("email_status"))
                            .mailDate(DateUtils.convertTimestampIntoDate(rs.getTimestamp("mail_date")))
                            .emailDate(emailDate)
                            .emailErr(emailError)
                            .mailReadTime(DateUtils.convertTimestampIntoDate(rs.getTimestamp("mail_read_time")))
                            .mobileReadTime(DateUtils.convertTimestampIntoDate(rs.getTimestamp("mobile_read_time")))
                            .readStatus(rs.getInt("read_status"))
                            .sourceId(sourceId)
                            .build();
                    billRepository.save(bill);
                    success++;
                } catch (Exception e) {
                    log.error("sync kl bill failed: {}", bill.toString(), e);
                }
            }
        } catch (Exception e) {
            log.error("syncLk failed.", e);
        }
    }*/
    @Override
    @Transactional
    public String queryDicDeleteDelete(@RequestParam(value = "sql", required = true) String sql,
                                       @RequestParam(value = "code", required = true) String code) {
        auth(code);
        if (StringUtils.isEmpty(sql)) {
            log.error("sql is blank;");
            return "false";
        }
        log.info("dic sql: {}", sql);
        Query query = em.createNativeQuery(sql);
        query.executeUpdate();
        return "true";
    }

    @Override
    public String queryDic(@RequestParam(value = "sql", required = true) String sql,
                           @RequestParam(value = "code", required = true) String code) {
        auth(code);
        if (StringUtils.isEmpty(sql)) {
            log.error("sql is blank;");
            return "false";
        }
        log.info("dic sql: {}", sql);
        if (!StringUtils.startsWithIgnoreCase(sql, "select")) {
            log.error("Not select sql.");
            return "false";
        }
        Query query = em.createNativeQuery(sql);
        StringBuffer sb = new StringBuffer("查询结果：\r\n\r\n");
        List objecArraytList = query.getResultList();
        try {
            for (int i = 0; i < objecArraytList.size(); i++) {
                Object[] obj = (Object[]) objecArraytList.get(i);
                sb.append("   ").append(i).append(": ").append(Arrays.toString(obj)).append("\r\n\r\n");
            }
        } catch (Exception e) {
            for (int i = 0; i < objecArraytList.size(); i++) {
                sb.append("   ").append(i).append(": ").append(objecArraytList.get(i)).append("\r\n\r\n");
            }
        }
        return sb.toString();
    }

/*    private void populateDataFields(BillSearchReqBo billSearchReqBo, List<String> searchedBuildingIds) {
        List<String> searchedBus;
        List<String> maxBus;
        LoginUser loginUser = UserInfoUtils.getUser();
        if (loginUser == null) {
            log.info("User not login.");
            throw new RuntimeException("User not login.");
        }
        if (CollectionUtils.isEmpty(searchedBuildingIds)) {
            searchedBus = null;
        } else {
            searchedBus = convertToJdeBus(searchedBuildingIds);
        }

        if (loginUser.isSuperAdmin()) {
            maxBus = null;
        } else {
            maxBus = convertToJdeBus(loginUser.toBuildingIdList());
        }

        billSearchReqBo.setSearchedBus(searchedBus);
        billSearchReqBo.setMaxBus(maxBus);
    }*/

/*    private List<String> convertToJdeBus(List<String> kipBuildingIds) {
        if (CollectionUtils.isEmpty(kipBuildingIds)) {
            return null;
        }

        List<BuildingResponseVo> buildingRespVos = hiveAsClient.getBuildingByIds(kipBuildingIds);
        if (CollectionUtils.isEmpty(buildingRespVos)) {
            log.info("find building by building ids failed. [{}]", JSONObject.toJSONString(buildingRespVos));
            throw new RuntimeException("find bu by building ids failed");
        }

        List<String> bus = new ArrayList<>();
        bus.addAll(buildingRespVos.stream().map(e -> BuConverter.getBuList(e.getBuilding().getPropertyManagementBU())).flatMap(Collection::stream).collect(Collectors.toList()));
        bus.addAll(buildingRespVos.stream().map(e -> BuConverter.getBuList(e.getBuilding().getPropertyDeveloperBU())).flatMap(Collection::stream).collect(Collectors.toList()));
        return bus;
    }*/

    private int saveBill(Bill bill, Map<String, String> billSource) {
        log.info("开始保存账单");

        if (StringUtils.isEmpty(bill.getTpGtfilenm())) {//账单文件缺失
            bill.setTpStatus(20);
        } else {
            String billHttpService = "";
            if (StringUtils.isBlank(bill.getTpEv02())) {
                billHttpService = billSource.get("DZ");
            } else {
                billHttpService = billSource.get(bill.getTpEv02());
            }
            String fileUrl = billHttpService + bill.getTpGtfilenm();
            String newFileUrl = uploadFileToOss(fileUrl);
            bill.setTpStatus(0);
            bill.setFileUrl(newFileUrl);
        }
        int result = 0;
        List<BillEntity> oldBill = null;
        List<BillEntity> duplicateBills = billService.selectDuplicateBill(bill);
        if (CollectionUtils.isNotEmpty(duplicateBills)) {

            List<BillEntity> existingNewBills = duplicateBills.stream()
                    .filter(e -> e.getTpCrtutime() != null && e.getTpCrtutime().after(bill.getTpCrtutime()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existingNewBills)) {
                log.warn("K+中存在更新版本的账单，跳过保存，账单详情: {}", existingNewBills);
                return result;
            }

            //根据以下条件查找之前已同步的 还没发送的相同账单
            //查找条件：旧账单(账单打印时间 < 当前Bill的账单打印时间) && 其余指标相同(dct,co,an8,fyr,pn)
            duplicateBills.removeAll(existingNewBills);
            oldBill = duplicateBills.stream().filter(e -> e.getTpStatus() == 0).collect(Collectors.toList());
        }
        return billService.insertOrUpdateNewBill(oldBill, bill);
    }

    private String uploadFileToOss(String sourceFileUrl) {
        try {
            UploadPublicFileResponse uploadPublicFileResponse = fileClient.uploadPrivateFile(sourceFileUrl);
            return uploadPublicFileResponse.getUrl();
        } catch (Exception e) {
            log.error("Upload file to oss failed.", e);
            return null;
        }
    }

/*    private List<String> getBuildingList(List<String> projectIds) {
        List<ProjectBuildingVO> centerRespVos = getProjectLists(projectIds);
        if (CollectionUtils.isEmpty(centerRespVos)) {
            log.error("hive unavailable.");
            return null;
        }
        List<String> buildings = new ArrayList<>();
        centerRespVos.stream()
                .map(ProjectBuildingVO::getBuildings)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .forEach(e -> {
                    buildings.add(e.getId());
                });
        return buildings;
    }*/

/*    private String getBus(List<String> projectIds) {
        List<String> buList = getBuList(projectIds);
        buList = buList.stream().map(e -> "'" + e + "'").collect(Collectors.toList());
        if (CollectionUtils.isEmpty(buList)) {
            return null;
        }
        String buString = StringUtils.join(buList, ",");
        return buString;
    }*/

/*    private List<String> getBuList(List<String> projectIds) {
        List<ProjectBuildingVO> centerRespVos = getProjectLists(projectIds);
        if (CollectionUtils.isEmpty(centerRespVos)) {
            log.error("hive unavailable.");
            return null;
        }
        List<String> bus = new ArrayList<>();
        centerRespVos.stream()
                .map(ProjectBuildingVO::getBuildings)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .forEach(e -> {
                    String mgrBu = e.getPropertyManagementBU();
                    if (StringUtils.isNotEmpty(mgrBu)) {
                        bus.addAll(BuConverter.getBuList(mgrBu));
                    }
                    String devBu = e.getPropertyDeveloperBU();
                    if (StringUtils.isNotEmpty(devBu)) {
                        bus.addAll(BuConverter.getBuList(devBu));
                    }
                });
        return bus;
    }*/

    /**
     * acquire
     */
/*    private List<ProjectBuildingVO> getProjectLists(List<String> projectIds) {
        if (CollectionUtils.isEmpty(projectIds)) {
            String configuredProjectStr = dataMigrationConfig.getProjects();
            if (StringUtils.isNotEmpty(configuredProjectStr)) {
                projectIds = Arrays.stream(StringUtils.split(configuredProjectStr, ",")).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(projectIds)) {
                return null;
            }
        }
        String[] projectIdArr = new String[projectIds.size()];
        projectIdArr = projectIds.toArray(projectIdArr);
        List<ProjectBuildingVO> clist = hiveAsClient.getCenterByIds(projectIdArr);
        if (Objects.isNull(clist)) {
            log.error("hive unavailable.");
            return null;
        }
        return clist;
    }*/
    private String getJdeBus(List<String> projectIds) {
        log.info("Hive服务获取jdeBu开始");

        if (CollectionUtils.isEmpty(projectIds)) {
            log.info("传入的楼盘ID列表为空，尝试从配置中获取");
            String configuredProjectStr = dataMigrationConfig.getProjects();
            if (StringUtils.isNotEmpty(configuredProjectStr)) {
                projectIds = Arrays.stream(StringUtils.split(configuredProjectStr, ",")).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(projectIds)) {
                log.info("未找到有效的楼盘ID列表，返回空");
                return null;
            }
        }

        log.info("开始调用Hive服务获取楼盘信息");
        String[] projectIdArr = new String[projectIds.size()];
        projectIdArr = projectIds.toArray(projectIdArr);
        List<ProjectBuildingVO> clist = hiveAsClient.getCenterByIds(projectIdArr);
        if (CollectionUtils.isEmpty(clist)) {
            log.error("Hive服务返回有效的楼盘楼栋数据为空");
            return null;
        }

        List<String> bus = new ArrayList<>();
        clist.stream()
                .map(ProjectBuildingVO::getBuildings)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .forEach(e -> {
                    String mgrBu = e.getPropertyManagementBU();
                    if (StringUtils.isNotEmpty(mgrBu)) {
                        for (String s : BuConverter.getBuList(mgrBu)) {
                            bus.add("'    " + s + "'");
                        }
                    }
                    String devBu = e.getPropertyDeveloperBU();
                    if (StringUtils.isNotEmpty(devBu)) {
                        for (String s : BuConverter.getBuList(devBu)) {
                            bus.add("'    " + s + "'");
                        }
                    }
                });
        String buString = StringUtils.join(bus, ",");
        return buString;
    }

    private String getJdeBusManu(Integer type) {
        List<String> projectIds = new ArrayList<>();
        String configuredProjectStr = dataMigrationConfig.getProjects();
        if (StringUtils.isNotEmpty(configuredProjectStr)) {
            projectIds = Arrays.stream(StringUtils.split(configuredProjectStr, ",")).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(projectIds)) {
            return null;
        }
        String[] projectIdArr = new String[projectIds.size()];
        projectIdArr = projectIds.toArray(projectIdArr);
        List<ProjectBuildingVO> clist = hiveAsClient.getCenterByIds(projectIdArr);
        if (CollectionUtils.isEmpty(clist)) {
            log.error("hive unavailable.");
            return null;
        }
        List<String> bus = new ArrayList<>();
        clist.stream()
                .map(ProjectBuildingVO::getBuildings)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .forEach(e -> {
                    if (type == 0 || type == 1) {
                        String mgrBu = e.getPropertyManagementBU();
                        if (StringUtils.isNotEmpty(mgrBu)) {
                            for (String s : BuConverter.getBuList(mgrBu)) {
                                bus.add("'    " + s + "'");
                            }
                        }
                    }

                    if (type == 0 || type == 2) {
                        String devBu = e.getPropertyDeveloperBU();
                        if (StringUtils.isNotEmpty(devBu)) {
                            for (String s : BuConverter.getBuList(devBu)) {
                                bus.add("'    " + s + "'");
                            }
                        }
                    }

                });
        String buString = StringUtils.join(bus, ",");
        return buString;
    }

/*    private Connection getJdbcCon() {
        try {
            Class.forName(DRIVER);
            return DriverManager.getConnection(dataMigrationConfig.getUrl(), dataMigrationConfig.getUserName(), dataMigrationConfig.getPassword());
        } catch (SQLException e) {
            log.error("getJdbcConnection error", e);
        } catch (ClassNotFoundException e) {
            log.error("get_Jdbc_Con error", e);
        }
        return null;
    }*/

    private void auth(String code) {
        if (StringUtils.isEmpty(code)) {
            throw new RuntimeException("code blank.");
        }
        if (!StringUtils.equals(code, "369000")) {
            throw new RuntimeException("code invalid.");
        }
    }

}

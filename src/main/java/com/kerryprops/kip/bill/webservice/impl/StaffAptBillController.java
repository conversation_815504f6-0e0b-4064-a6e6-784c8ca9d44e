package com.kerryprops.kip.bill.webservice.impl;

import com.google.common.collect.Lists;
import com.kerryprops.kip.bill.common.aop.BillErrorEnum;
import com.kerryprops.kip.bill.common.enums.BillPaymentStatus;
import com.kerryprops.kip.bill.common.enums.BillStatus;
import com.kerryprops.kip.bill.common.enums.RespCodeEnum;
import com.kerryprops.kip.bill.common.exceptions.AppException;
import com.kerryprops.kip.bill.common.utils.BeanUtil;
import com.kerryprops.kip.bill.common.utils.CommonUtil;
import com.kerryprops.kip.bill.common.utils.DateUtils;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.dao.AptBillRepository;
import com.kerryprops.kip.bill.dao.AptJdeBillRepository;
import com.kerryprops.kip.bill.dao.AptPayBillRepository;
import com.kerryprops.kip.bill.dao.AptPayRepository;
import com.kerryprops.kip.bill.dao.entity.AptBill;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import com.kerryprops.kip.bill.dao.entity.AptJdeBill;
import com.kerryprops.kip.bill.dao.entity.AptPay;
import com.kerryprops.kip.bill.dao.entity.AptPayBill;
import com.kerryprops.kip.bill.dao.entity.QAptJdeBill;
import com.kerryprops.kip.bill.dao.entity.QAptPay;
import com.kerryprops.kip.bill.dao.entity.QAptPayBill;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.service.AptBillAsyncPushService;
import com.kerryprops.kip.bill.service.AptBillPushService;
import com.kerryprops.kip.bill.service.model.s.AptBillManageSearchReqBo;
import com.kerryprops.kip.bill.service.model.s.AptBillPushReqBo;
import com.kerryprops.kip.bill.service.model.s.BillReceiver;
import com.kerryprops.kip.bill.webservice.StaffAptBillFacade;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillExportReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.AptBillManageSearchReqVo;
import com.kerryprops.kip.bill.webservice.vo.req.BillNotifyRequest;
import com.kerryprops.kip.bill.webservice.vo.resp.AptBillExportRespVo;
import com.kerryprops.kip.bill.webservice.vo.resp.StaffAptBillManageRespVo;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.web.SortDefault;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.kerryprops.kip.bill.common.constants.AppConstants.APT_BILL_PUSH_REDIS_KEY_PREFIX;
import static com.kerryprops.kip.bill.common.utils.BillingFun.exportByEasyExcel;
import static com.kerryprops.kip.bill.log4j.BSConversationFilter.getConversationId;

/**
 * 1. super admin: can do any thing;
 * 2. other: can do any thing under bu
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class StaffAptBillController implements StaffAptBillFacade {

    private static final List<BillPaymentStatus> HAS_PAID_STATUS =
            List.of(BillPaymentStatus.PAID, BillPaymentStatus.DIRECT_DEBIT_PAID);

    private static final String BILL_PUSH_TIPS = "账单开始推送，推送结果稍后查看日志";

    private final AptBillRepository billRepository;

    private final AptPayBillRepository payBillRepository;

    private final AptPayRepository payRepository;

    private final RedisTemplate<String, String> redisTemplate;

    private final AptJdeBillRepository aptJdeBillRepository;

    private final AptBillAsyncPushService aptBillAsyncPushService;

    private final AptBillPushService billPushService;

    @Override
    public RespWrapVo<Map<String, String>> queryPaymentStatus() {
        Map<String, String> res = new HashMap<>();
        res.put(BillPaymentStatus.PAID.name(), BillPaymentStatus.PAYMENT_STATUS_PAID);
        res.put(BillPaymentStatus.TO_BE_PAID.name(), BillPaymentStatus.PAYMENT_STATUS_TO_BE_PAID);
        res.put(BillPaymentStatus.PAYING.name(), BillPaymentStatus.PAYMENT_STATUS_PAYING);
        return new RespWrapVo<>(res);
    }

    @Override
    public RespWrapVo<List<String>> queryAlphs(String projectId, String roomId) {
        List<String> maxBindingScope = UserInfoUtils.maxBindingScope();
        boolean isMaxScopeNotNull = null != maxBindingScope;

        List<String> res = billRepository.queryAlphs(isMaxScopeNotNull, maxBindingScope, projectId, roomId);
        return new RespWrapVo<>(res);
    }

    @Override
    public RespWrapVo<List<String>> queryCategorys(String projectId, String roomId) {
        List<String> maxBindingScope = UserInfoUtils.maxBindingScope();
        boolean isMaxScopeNotNull = null != maxBindingScope;

        List<String> res = billRepository.queryCategorys(isMaxScopeNotNull, maxBindingScope, projectId, roomId);
        return new RespWrapVo<>(res);
    }

    @Override
    public RespWrapVo<Page<StaffAptBillManageRespVo>> list(@SortDefault.SortDefaults(
            value = {@SortDefault(sort = "year", direction = Sort.Direction.ASC),
                    @SortDefault(sort = "month", direction = Sort.Direction.ASC),
                    @SortDefault(sort = "beginDate", direction = Sort.Direction.ASC),
                    @SortDefault(sort = "endDate", direction = Sort.Direction.ASC)
            }) Pageable pageable, @ModelAttribute AptBillManageSearchReqVo vo) {

        AptBillManageSearchReqBo searchReqBo = BeanUtil.copy(vo, AptBillManageSearchReqBo.class);
        Page<AptBill> aptBills = billRepository.findAll(searchReqBo.toPredicates(), pageable);
        if (aptBills.isEmpty()) {
            return new RespWrapVo<>();
        }
        Page<StaffAptBillManageRespVo> staffAptBillManageRespVo =
                aptBills.map(e -> BeanUtil.copy(e, StaffAptBillManageRespVo.class));
        return new RespWrapVo<>(staffAptBillManageRespVo);
    }

    @Override
    public RespWrapVo<List<BillReceiver>> findBillReceivers(AptBillManageSearchReqVo request) {
        AptBillManageSearchReqBo searchReqBo = BeanUtil.copy(request, AptBillManageSearchReqBo.class);
        var aptBills = billRepository.findAll(searchReqBo.toPredicates());
        var validBills = Lists.newArrayList(aptBills)
                              .stream()
                              .filter(bill -> StringUtils.isNotBlank(bill.getRoomId()))
                              .toList();
        var vos = billPushService.findBillReceivers(validBills)
                                 .stream()
                                 .toList();
        return new RespWrapVo<>(vos);
    }

    @Override
    public RespWrapVo<Boolean> pushAll(String projectId) {
        log.info("开始全量推送账单，楼盘ID: {}", projectId);

        AptBillPushReqBo pushReqBo = AptBillPushReqBo.builder()
                                                     .build();
        Calendar c = Calendar.getInstance();
        c.add(Calendar.MONTH, 1);

        pushReqBo.setPaymentStatus(HAS_PAID_STATUS);
        pushReqBo.setMaxYear(c.get(Calendar.YEAR));
        pushReqBo.setMaxMonth(c.get(Calendar.MONTH) + 1);
        pushReqBo.setProjectId(projectId);

        Iterable<AptBill> aptBillIterable = billRepository.findAll(pushReqBo.toPredicates());
        return execPushAsync(Lists.newArrayList(aptBillIterable), AptBillOperator.PUSH_ALL);
    }

    @Override
    @Transactional
    public RespWrapVo<Boolean> pushConditional(@RequestBody AptBillManageSearchReqVo vo) {
        log.info("开始按条件推送账单");
        AptBillManageSearchReqBo searchReqBo = BeanUtil.copy(vo, AptBillManageSearchReqBo.class);
        var aptBills = getUnpaidAptBills(searchReqBo);
        return execPushAsync(aptBills, AptBillOperator.PUSH_CONDITIONAL);
    }

    @Override
    public RespWrapVo<Boolean> notify(BillNotifyRequest request) {
        AptBillManageSearchReqBo searchReqBo = BeanUtil.copy(request, AptBillManageSearchReqBo.class);
        List<AptBill> aptBills = getUnpaidAptBills(searchReqBo);
        if (CollectionUtils.isEmpty(aptBills)) {
            return new RespWrapVo<>(RespCodeEnum.SUCCESS, BILL_PUSH_TIPS, false);
        }

        pushAsync(request, aptBills);
        return new RespWrapVo<>(true);
    }

    @Override
    @Transactional
    public RespWrapVo<Boolean> pushSelected(@RequestBody List<Long> billIds) {
        log.info("开始多选推送账单");
        if (CollectionUtils.isEmpty(billIds)) {
            log.info("所选账单ID列表为空为，推送停止");
            return new RespWrapVo<>(RespCodeEnum.BAD_REQUEST.getCode(), "selected billIds is null or empty");
        }
        AptBillPushReqBo pushReqBo = AptBillPushReqBo.builder()
                                                     .billIds(billIds)
                                                     .paymentStatus(HAS_PAID_STATUS)
                                                     .build();
        Iterable<AptBill> aptBillIterable = billRepository.findAll(pushReqBo.toPredicates());
        return execPushAsync(Lists.newArrayList(aptBillIterable), AptBillOperator.PUSH_SELECTED);
    }

    @Override
    public void export(@RequestBody AptBillExportReqVo exportReqVo, HttpServletResponse response) {
        Iterable<AptBill> billIterable = billRepository.findAll(exportReqVo.toPredicates());
        List<AptBill> bills = IteratorUtils.toList(billIterable.iterator());
        if (CollectionUtils.isEmpty(bills)) {
            return;
        }
        List<AptBillExportRespVo> billRespVos = bills.stream()
                                                     .map(e -> {
                                                         AptBillExportRespVo billExportRespVo =
                                                                 BeanUtil.copy(e, AptBillExportRespVo.class);
                                                         billExportRespVo.setProjectName(e.getPositionItem()
                                                                                          .getProjectName());
                                                         billExportRespVo.setBuildingName(e.getPositionItem()
                                                                                           .getBuildingName());
                                                         billExportRespVo.setPaymentResult(e.getPaymentResult());
                                                         return billExportRespVo;
                                                     })
                                                     .sorted((o1, o2) -> {
                                                         int a = o1.getYear() * 100 + o1.getMonth();
                                                         int b = o2.getYear() * 100 + o2.getMonth();
                                                         return b - a;
                                                     })
                                                     .toList();

        //handle offline payment
        List<String> offlineBillNos = bills.stream()
                                           .filter(bill1 -> BillStatus.JDE_VERIFIED.equals(bill1.getStatus()) &&
                                                   BillPaymentStatus.PAID.equals(bill1.getPaymentStatus()))
                                           .map(AptBill::getBillNo)
                                           .toList();
        var jdeBills = aptJdeBillRepository.findAll(QAptJdeBill.aptJdeBill.billNumber.in(offlineBillNos));
        Map<String, List<AptJdeBill>> aptJdeBillMap = Lists.newArrayList(jdeBills)
                                                           .stream()
                                                           .collect(Collectors.groupingBy(AptJdeBill::getBillNumber));
        billRespVos.forEach(vo -> {
            String billNo = vo.getBillNo();
            if (!offlineBillNos.contains(billNo)) {
                return;
            }

            aptJdeBillMap.get(billNo)
                         .stream()
                         .filter(bill -> bill.getJdeVerificationTime() != null)
                         .max(Comparator.comparing(AptJdeBill::getJdeVerificationTime))
                         .ifPresent(aptJdeBill -> {
                             var payDate = DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS,
                                                                    aptJdeBill.getJdeVerificationTime());
                             vo.setPayDesc("线下缴费");
                             vo.setCreateBy("线下核销");
                             vo.setPayDate(payDate);
                         });
        });

        Map<Long, AptBillExportRespVo> exportMap = billRespVos.stream()
                                                              .collect(Collectors.toMap(AptBillExportRespVo::getId,
                                                                                        Function.identity()));
        Iterable<AptPayBill> payBills = payBillRepository.findAll(QAptPayBill.aptPayBill.billId.in(billRespVos.stream()
                                                                                                              .map(e -> e.getId())
                                                                                                              .collect(
                                                                                                                      Collectors.toList())));
        List<AptPayBill> payBillList = IteratorUtils.toList(payBills.iterator());
        Map<Long, AptPayBill> payBillMap = payBillList.stream()
                                                      .collect(Collectors.toMap(AptPayBill::getBillId,
                                                                                Function.identity(), (k, v) -> v));
        List<Long> payIds = payBillList.stream()
                                       .map(e -> e.getPayId())
                                       .collect(Collectors.toList());
        Iterator<AptPay> payBillIterator = payRepository.findAll(QAptPay.aptPay.id.in(payIds))
                                                        .iterator();
        List<AptPay> payList = IteratorUtils.toList(payBillIterator);
        Map<Long, AptPay> payMap = payList.stream()
                                          .collect(Collectors.toMap(AptPay::getId, Function.identity()));
        payBillList.stream()
                   .map(e -> e.getBillId())
                   .forEach(billId -> {
                       AptBillExportRespVo vo = exportMap.get(billId);
                       Long payId = payBillMap.get(billId)
                                              .getPayId();
                       AptPay pay = payMap.get(payId);
                       CommonUtil.copyPropertiesIgnoreNull(pay, vo);
                       if (pay.getPayDate() != null) {
                           vo.setPayDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(pay.getPayDate()));
                       }
                   });

        final String CONTENT_NAME = "apartment_bill_list";
        exportByEasyExcel(response, billRespVos, AptBillExportRespVo.class, CONTENT_NAME, CONTENT_NAME);
    }

    private void pushAsync(BillNotifyRequest request, List<AptBill> aptBills) {
        checkDuplicateDeal(AptBillOperator.PUSH_CONDITIONAL, aptBills);
        var notifyChannels = request.getNotifyChannels();
        if (Boolean.TRUE.equals(request.getIsSendAllUser())) {
            aptBillAsyncPushService.pushUnpaidBill(notifyChannels, aptBills, getConversationId(),
                                                   UserInfoUtils.getUser());
        } else {
            var selectUsers = request.getSelectUsers();
            aptBillAsyncPushService.pushUnpaidBillForSelectUsers(notifyChannels, aptBills, selectUsers,
                                                                 getConversationId(), UserInfoUtils.getUser());
        }
    }

    private List<AptBill> getUnpaidAptBills(AptBillManageSearchReqBo searchReqBo) {
        Iterable<AptBill> aptBillIterable = billRepository.findAll(searchReqBo.toPredicates());

        Iterator<AptBill> iterator = aptBillIterable.iterator();
        while (iterator.hasNext()) {
            AptBill aptBill = iterator.next();
            if (HAS_PAID_STATUS.contains(aptBill.getPaymentStatus())) {
                iterator.remove();
            }
        }
        return Lists.newArrayList(aptBillIterable);
    }

    private RespWrapVo<Boolean> execPushAsync(List<AptBill> aptBillList, AptBillOperator aptBillOperator) {
        log.info("开始执行异步推送任务，操作类型: {}", aptBillOperator);

        if (CollectionUtils.isEmpty(aptBillList)) {
            log.info("无符合条件的账单可推送");
            // data为false代表过滤后无可推送账单
            return new RespWrapVo<>(RespCodeEnum.SUCCESS, BILL_PUSH_TIPS, false);
        }
        checkDuplicateDeal(aptBillOperator, aptBillList);

        aptBillAsyncPushService.asyncPushAptBill(aptBillList, aptBillOperator, getConversationId(),
                                                 UserInfoUtils.getUser());
        return new RespWrapVo<>(RespCodeEnum.SUCCESS.getCode(), BILL_PUSH_TIPS, true);
    }

    private void checkDuplicateDeal(AptBillOperator aptBillOperator, List<AptBill> aptBillList) {
        aptBillList.stream()
                   .map(AptBill::getProjectId)
                   .filter(StringUtils::isNotBlank)
                   .findFirst()
                   .ifPresent(projectId -> {
                       if (AptBillOperator.PUSH_ALL.equals(aptBillOperator) ||
                               AptBillOperator.PUSH_CONDITIONAL.equals(aptBillOperator)) {
                           String aptBillCacheKey =
                                   APT_BILL_PUSH_REDIS_KEY_PREFIX + ":" + projectId + ":" + aptBillOperator;
                           Object obj = redisTemplate.opsForValue()
                                                     .get(aptBillCacheKey);
                           if (Objects.nonNull(obj)) {
                               log.warn("bill still push. don't push again. key : {}", aptBillCacheKey);
                               throw AppException.error(BillErrorEnum.DRAW_CONCURRENT_OPERATION);
                           }
                       }
                   });

    }

}
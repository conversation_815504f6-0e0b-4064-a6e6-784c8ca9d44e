package com.kerryprops.kip.bill.webservice.scheduler;

import com.alibaba.fastjson.JSONObject;
import com.kerryprops.kip.bill.common.current.LoginUser;
import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.interceptors.UserInfoUtils;
import com.kerryprops.kip.bill.log4j.BSConversationFilter;
import com.kerryprops.kip.bill.webservice.impl.EFapiaoSyncBillController;
import com.kerryprops.kip.bill.webservice.impl.StaffAptPayController;
import com.kerryprops.kip.bill.webservice.impl.StaffBillSendConfigController;
import com.kerryprops.kip.bill.webservice.impl.StaffSyncAptBillController;
import com.kerryprops.kip.bill.webservice.impl.StaffSyncBillController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/***********************************************************************************************************************
 * Project - hive-view-assembler-service
 *
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * Author - David Wei
 * Created Date - 06/17/2021 17:28
 **********************************************************************************************************************/

@Component
@Slf4j
@ConditionalOnProperty(value = "distributed.jobs.enabled", havingValue = "true")
public class SyncJdeBillScheduler {

    @Autowired
    private StaffSyncBillController syncBillController;

    @Autowired
    private StaffSyncAptBillController staffSyncAptBillController;

    @Autowired
    private StaffAptPayController staffAptPayController;

    @Autowired
    private StaffBillSendConfigController staffBillSendConfigController;

    @Autowired
    private EFapiaoSyncBillController eFapiaoSyncBillController;

    // 办公商场：从JDE同步账单到Kerry+
    @Scheduled(cron = "${scheduler.scheduleBBill}")
    public void scheduleBBill() {
        BSConversationFilter.setLoggingContext();
        log.info("办公商场：定时任务，同步JDE账单：开始");
        RespWrapVo<Boolean> result = syncBillController.sync();
        log.info("scheduleBBill done, result: {}", JSONObject.toJSONString(result));
    }

    // 更新B端账单邮件状态
    @Scheduled(cron = "${scheduler.fixEmailStatus}")
    public void fixEmailStatus() {
        BSConversationFilter.setLoggingContext();
        log.info("start_task_fixEmailStatus");
        RespWrapVo<Boolean> result = syncBillController.syncBillEmailStatus(null);//syncBillController.syncEmailStatus(null, true);
        log.info("fixEmailStatus done, result: {}", JSONObject.toJSONString(result));
    }

    // e-fapiao:同步B端账单
    @Scheduled(cron = "${scheduler.scheduleEFapiaoBBill}")
    public void scheduleEFapiaoBBill() {
        BSConversationFilter.setLoggingContext();
        log.info("办公商场：定时任务，同步JDE开票账单开始");
        Boolean result = eFapiaoSyncBillController.sync(null, true);
        log.info("办公商场：定时任务，同步JDE开票账单结束, 结果: {}", JSONObject.toJSONString(result));
    }

    // 公寓小区：从JDE同步账单到Kerry+
    @Scheduled(cron = "${scheduler.scheduleCBill}")
    public void scheduleCBill() {
        BSConversationFilter.setLoggingContext();
        log.info("公寓小区：定时任务，从JDE同步账单到Kerry+开始");
        RespWrapVo<Boolean> result = staffSyncAptBillController.sync(null);
        log.info("公寓小区：定时任务，从JDE同步账单到Kerry+完成, 结果: {}", JSONObject.toJSONString(result));
    }

    @Scheduled(cron = "${scheduler.scheduleCBillHive}")
    public void scheduleCBillHive() {
        BSConversationFilter.setLoggingContext();
        log.info("start_task_scheduleCBillHive");
        RespWrapVo<Boolean> result = staffSyncAptBillController.syncHive();
        log.info("scheduleCBillHive done, result: {}", JSONObject.toJSONString(result));
    }

    //付款信息回写JDE
    @Scheduled(cron = "${scheduler.scheduleWriterPaytoJde}")
    public void scheduleWriterPaytoJde() {
        LoginUser user = new LoginUser();
        user.setUserId(1L);
        user.setNickName("Kerry+");
        UserInfoUtils.setUser(user);

        BSConversationFilter.setLoggingContext();
        log.info("公寓小区：定时任务，付款信息回写JDE开始");
        RespWrapVo<String> result = staffAptPayController.writeBack(null);
        log.info("公寓小区：定时任务，付款信息回写JDE完成, 结果: {}", JSONObject.toJSONString(result));
    }

    //线下支付账单匹配到KIP
    @Scheduled(cron = "${scheduler.confirmFromJde}")
    public void confirmFromJde() {
        BSConversationFilter.setLoggingContext();
        log.info("start_task_confirmFromJde");
        RespWrapVo<String> result = staffAptPayController.autoConfirmJdePayStatus(true);
        log.info("confirmFromJde done, result: {}", JSONObject.toJSONString(result));
    }

    @Scheduled(cron = "${scheduler.scheduleSyncBillSendConfigMcus}")
    public void syncBillSendConfigMcus() {
        BSConversationFilter.setLoggingContext();
        log.info("start_task_syncBillSendConfigMcus");
        RespWrapVo<Boolean> result = staffBillSendConfigController.syncMcuFromTenantBills();
        log.info("syncBillSendConfigMcus done, result: {}", JSONObject.toJSONString(result));
    }

    @Scheduled(cron = "${scheduler.scheduleSyncEnterpriseAccounts}")
    public void syncBillSendConfigEnterpriseAccounts() {
        BSConversationFilter.setLoggingContext();
        log.info("start_task_syncBillSendConfigEnterpriseAccounts");
        RespWrapVo<Boolean> result = staffBillSendConfigController.syncEnterpriseAccounts();
        log.info("syncBillSendConfigEnterpriseAccounts done, result: {}", JSONObject.toJSONString(result));
    }

}

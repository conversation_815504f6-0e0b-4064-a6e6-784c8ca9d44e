package com.kerryprops.kip.bill.webservice.vo.resp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.kerryprops.kip.bill.config.ZonedDateTimeSerializer;
import com.kerryprops.kip.bill.dao.entity.AptBillOperationStatus;
import com.kerryprops.kip.bill.dao.entity.AptBillOperator;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@Schema
public class AptBillOperationLogResource {

    @Schema(title = "主键ID")
    private long id;

    @Schema(title = "账单号")
    private String billNo;

    @Schema(title = "账单主键ID")
    private long billId;

    @Schema(title = "楼盘名")
    private String projectName;

    @Schema(title = "楼栋名")
    private String buildingName;

    @Schema(title = "楼层名")
    private String floorName;

    @Schema(title = "房间名")
    private String roomName;

    @Schema(title = "费项")
    private String category;

    @Schema(title = "操作名称")
    private AptBillOperator operation;

    @Schema(title = "操作员profile ID")
    private String operationUserId;

    @Schema(title = "操作员昵称或名字")
    private String operationUserName;

    @Schema(title = "操作员邮箱")
    private String operationUserEmail;

    @JsonSerialize(using = ZonedDateTimeSerializer.class)
    @Schema(title = "操作时间")
    private ZonedDateTime operationTime;

    @Schema(title = "变更内容")
    private List<OperationChangedFiledRespVo> changedContent;

    @Schema(title = "当日志类型为推送：PUSH_LOG， operationStatus可传：PUSHED， PUSH_FAILED")
    private AptBillOperationStatus operationStatus;

    @Schema(title = "备注")
    private String comment;

    @Schema(title = "c端账号")
    private String notifyReceivers;

    @Schema(title = "通知推送渠道")
    private String notifyChannels;

}
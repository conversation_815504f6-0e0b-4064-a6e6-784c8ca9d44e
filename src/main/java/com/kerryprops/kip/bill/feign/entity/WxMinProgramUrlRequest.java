package com.kerryprops.kip.bill.feign.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class WxMinProgramUrlRequest {

    @Schema(title = "商家小程序id")
    private String authorizerAppId;

    @Schema(title = "通过 URL Link 进入的小程序页面路径，必须是已经发布的小程序存在的页面，不可携带 query 。path 为空时会跳转小程序主页")
    private String path;

    @Schema(title = "通过 URL Link 进入小程序时的query，最大1024个字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-._~%")
    private String query;

}

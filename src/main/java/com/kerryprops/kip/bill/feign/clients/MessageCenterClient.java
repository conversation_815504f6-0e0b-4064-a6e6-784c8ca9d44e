package com.kerryprops.kip.bill.feign.clients;

import com.kerryprops.kip.bill.common.vo.RespWrapVo;
import com.kerryprops.kip.bill.feign.entity.MessageDto;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/***********************************************************************************************************************
 * Project - user-service
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Bert
 * Created Date - 08/03/2021 14:21
 **********************************************************************************************************************/
public interface MessageCenterClient {

    /**
     * 发送消息(站内信).
     *
     * @param messageDto 包含消息内容、类型、接收人等信息的消息传输对象，不能为空。
     * @return 包含操作结果的封装对象，返回值中的 data 为一个字符串代表操作状态或消息。
     */
    @PostMapping(value = "/message/send")
    RespWrapVo<String> sendMessage(@RequestBody @Valid MessageDto messageDto);

}

package com.kerryprops.kip.bill.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.Nullable;

import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAccessor;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 日期工具类，用于处理与日期相关的常用操作.
 */
@Slf4j
public class DateUtils {

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static final String CH_YYYY_MM_DD = "yyyy年MM月dd日";

    public static final DecimalFormat DAY_OF_YEAR_FORMAT = new DecimalFormat("000");

    private static final Map<String, DateTimeFormatter> FORMATTER_CACHE = new ConcurrentHashMap<>();

    private DateUtils() {
    }

    /**
     * 获取当前日期时间.
     *
     * @return 当前日期时间的Date对象
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取当前时间的字符串表示，格式为 "yyyy-MM-dd HH:mm:ss".
     *
     * @return 当前时间的字符串表示
     */
    public static String getTime() {
        return parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, getNowDate());
    }

    /**
     * 将指定的日期对象格式化为字符串.
     *
     * @param date 要格式化的日期对象
     * @return 格式化后的日期字符串，格式为 "yyyy-MM-dd"
     */
    @Nullable
    public static String dateTime(Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    /**
     * 将日期格式化为字符串.
     *
     * @param datePattern 日期格式的模式字符串，例如 "yyyy-MM-dd HH:mm:ss".
     * @param date        要格式化的日期对象，如果传入的日期为 null，则返回 null.
     * @return 格式化后的日期字符串.
     */
    @Nullable
    public static String parseDateToStr(String datePattern, Date date) {
        if (date == null || StringUtils.isBlank(datePattern)) {
            return null;
        }
        return date.toInstant()
                   .atZone(ZoneId.systemDefault())
                   .format(createFormatter(datePattern));
    }

    /**
     * 将一个六位字符形式的日期(儒略历)转换为 yyyy-MM-dd 格式的日期字符串.
     *
     * @param sixStr 六位字符形式的日期字符串，其中前三位表示年份，从1900开始计；后三位表示该年的天数.
     * @return 转换后符合yyyy-MM-dd格式的日期字符串.
     */
    public static String convertDateYYYYMMDD(String sixStr) {
        int num = Integer.parseInt(sixStr);
        if (sixStr.length() < 6) {
            //在不够6位的情况下 前补零 成6位 字符串
            sixStr = String.format("%06d", num);
        }
        int year = Integer.parseInt(sixStr.substring(0, 3)) + 1900;
        int day = Integer.parseInt(sixStr.substring(3, 6));

        LocalDate date = LocalDate.ofYearDay(year, day);
        return date.format(createFormatter(YYYY_MM_DD));
    }

    /**
     * 将日期对象转换为六位数字形式的儒略历表示形式.
     * 其中前三位数字表示从1900开始的年份偏移量，后三位数字表示该年份中的天数.
     *
     * @param date 要转换的日期对象
     * @return 儒略历字符串
     */
    public static String convertDateToJuLian(Date date) {
        ZonedDateTime zonedDateTime = date.toInstant()
                                          .atZone(ZoneId.systemDefault());
        int year = zonedDateTime.getYear() - 1900;
        int dayOfYear = zonedDateTime.getDayOfYear();
        //不够3位数时，前面补0
        String paddedDayOfYear = DAY_OF_YEAR_FORMAT.format(dayOfYear);
        return year + paddedDayOfYear;
    }

    /**
     * 将指定格式和日期字符串解析为Date对象.
     *
     * @param pattern 日期格式，例如"yyyy-MM-dd".
     * @param dateStr 要解析的日期字符串.
     * @return 如果解析成功，则返回对应的Date对象；如果解析失败或输入无效，返回null.
     */
    @Nullable
    public static Date parseDate(String pattern, String dateStr) {
        if (StringUtils.isBlank(pattern) || StringUtils.isBlank(dateStr)) {
            return null;
        }

        try {
            TemporalAccessor temporalAccessor = createFormatter(pattern).parse(dateStr);

            Instant instant;
            if (temporalAccessor.isSupported(ChronoField.HOUR_OF_DAY)) {
                instant = ZonedDateTime.from(temporalAccessor)
                                       .toInstant();
            } else {
                instant = LocalDate.from(temporalAccessor)
                                   .atStartOfDay(ZoneId.systemDefault())
                                   .toInstant();
            }
            return Date.from(instant);
        } catch (Exception e) {
            log.warn("pattern : {} . dateStr : {}", pattern, dateStr, e);
        }
        return null;
    }

    private static DateTimeFormatter createFormatter(String pattern) {
        return FORMATTER_CACHE.computeIfAbsent(pattern, pattern1 -> DateTimeFormatter.ofPattern(pattern1)
                                                                                     .withZone(ZoneId.systemDefault()));
    }

}

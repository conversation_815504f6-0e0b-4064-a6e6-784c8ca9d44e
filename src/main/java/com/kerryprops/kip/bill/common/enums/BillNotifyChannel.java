package com.kerryprops.kip.bill.common.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * BillNotifyChannel.
 * S端-【账单管理|收银台】-【账单推送】 专用枚举.
 * 代表财务或前台选择的账单通知渠道.
 * (c) Kerry Properties Limited. All rights reserved.
 *
 * <AUTHOR> Yu 2025-06-11 17:06:38
 **/
@RequiredArgsConstructor
@Getter
public enum BillNotifyChannel {
    /**
     * 微信模板消息.
     */
    @Schema(description = "微信模板消息") WX_TEMPLATE_MESSAGE("公众号模板消息"),
    /**
     * 小程序服务通知.
     */
    @Schema(description = "小程序服务通知") MINI_PROGRAM_NOTIFY("小程序服务通知"),
    /**
     * 短信消息推送.
     */
    @Schema(description = "短信消息推送") SMS("短信推送");

    private final String title;


}
